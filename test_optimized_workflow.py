#!/usr/bin/env python3
"""
测试优化后的工作流框架
验证 reasoning 字段和强化的 prompt 约束是否正常工作
"""

import asyncio
import json
from sub_agents.sql_database_chain.violation_data_search_new.workflow.generic_workflow_framework import (
    create_generic_workflow,
    WorkLevelPlanner,
    TaskLevelPlanner,
    IterationDecider,
    ToolRegistry,
    GenericQueryExecutor
)
from sub_agents.rep.utils.llm_utils import get_a_llm
from langchain_core.messages import HumanMessage


async def test_work_level_planner():
    """测试工作级规划器的 reasoning 功能"""
    print("=== 测试工作级规划器 ===")
    
    llm = get_a_llm(llm_type='hs-deepseek-v3-0324', temperature=0)
    planner = WorkLevelPlanner(llm)
    
    message = HumanMessage(content="分析过去三年的违规案例趋势并生成详细报告")
    result = await planner.plan(message)
    
    print("工作级规划结果:")
    print(f"思考过程: {result.get('reasoning', 'N/A')}")
    print(f"用户目标: {result.get('user_goal', 'N/A')}")
    print(f"子目标: {result.get('sub_goals', [])}")
    print(f"策略: {result.get('strategy', 'N/A')}")
    print(f"工作阶段数: {len(result.get('work_phases', []))}")
    print()


async def test_task_level_planner():
    """测试任务级规划器的强化 prompt 和 reasoning 功能"""
    print("=== 测试任务级规划器 ===")
    
    llm = get_a_llm(llm_type='hs-deepseek-v3-0324', temperature=0)
    tool_registry = ToolRegistry()
    tool_registry.register("query_tool", GenericQueryExecutor(), "通用查询工具，支持向量搜索")
    
    planner = TaskLevelPlanner(llm, tool_registry)
    
    # 模拟工作级规划结果
    work_plan = {
        "reasoning": "用户需要了解违规案例的发展趋势，这是一个复杂的分析任务。",
        "user_goal": "了解违规案例发展趋势，为决策提供数据支持",
        "sub_goals": ["获取历史数据", "识别趋势模式", "生成分析报告"],
        "strategy": "数据驱动的趋势分析方法",
        "required_capabilities": ["数据查询", "趋势分析", "报告生成"],
        "work_phases": [
            {"phase": "数据收集", "objective": "获取完整的历史违规数据", "deliverable": "结构化数据集"},
            {"phase": "趋势分析", "objective": "识别时间和类型趋势", "deliverable": "趋势分析结果"},
            {"phase": "报告生成", "objective": "形成可视化分析报告", "deliverable": "详细分析报告"}
        ]
    }
    
    available_tools = tool_registry.get_available_tools_desc()
    result = await planner.plan(work_plan, available_tools)
    
    print("任务级规划结果:")
    print(f"思考过程: {result.get('reasoning', 'N/A')}")
    print(f"用户目标: {result.get('user_goal', 'N/A')}")
    print(f"解决思路: {result.get('approach', 'N/A')}")
    print(f"任务数量: {len(result.get('initial_tasks', []))}")
    print(f"需要图表: {result.get('need_chart', False)}")
    print(f"执行策略: {result.get('execution_strategy', 'N/A')}")
    
    # 检查任务详情
    for i, task in enumerate(result.get('initial_tasks', []), 1):
        print(f"任务{i}:")
        print(f"  - ID: {task.get('id', 'N/A')}")
        print(f"  - 描述: {task.get('description', 'N/A')}")
        print(f"  - 查询: {task.get('query', 'N/A')}")
        print(f"  - 状态: {task.get('status', 'N/A')}")
        print(f"  - 优先级: {task.get('priority', 'N/A')}")
        print(f"  - 依赖: {task.get('dependencies', [])}")
    print()


async def test_iteration_decider():
    """测试迭代决策器的 reasoning 功能"""
    print("=== 测试迭代决策器 ===")
    
    llm = get_a_llm(llm_type='hs-deepseek-v3-0324', temperature=0)
    decider = IterationDecider(llm)
    
    # 模拟当前结果
    current_results = [
        {
            "description": "收集2021-2024年违规案例数据",
            "result": "获取到1000条违规案例数据，包含时间、类型、处罚金额等信息"
        },
        {
            "description": "分析违规案例类型分布",
            "result": "主要违规类型：信息披露违规(40%)、财务造假(30%)、内幕交易(20%)、其他(10%)"
        }
    ]
    
    messages = [HumanMessage(content="分析过去三年的违规案例趋势并生成详细报告")]
    
    result = await decider.decide(
        user_goal="了解违规案例发展趋势，为决策提供数据支持",
        approach="数据驱动的趋势分析方法",
        current_results=current_results,
        messages=messages,
        need_chart=True
    )
    
    print("迭代决策结果:")
    print(f"思考过程: {result.get('reasoning', 'N/A')}")
    print(f"需要更多任务: {result.get('need_more_tasks', False)}")
    print(f"新任务数量: {len(result.get('new_tasks', []))}")
    
    # 检查新任务详情
    for i, task in enumerate(result.get('new_tasks', []), 1):
        print(f"新任务{i}:")
        print(f"  - ID: {task.get('id', 'N/A')}")
        print(f"  - 描述: {task.get('description', 'N/A')}")
        print(f"  - 查询: {task.get('query', 'N/A')}")
        print(f"  - 意图: {task.get('intent', 'N/A')}")
    print()


async def test_complete_workflow():
    """测试完整的工作流"""
    print("=== 测试完整工作流 ===")
    
    workflow = create_generic_workflow()
    
    # 测试复杂任务
    test_query = "分析最近三年的违规案例趋势，重点关注处罚金额变化和违规类型分布"
    
    print(f"测试查询: {test_query}")
    print("开始执行工作流...")
    
    try:
        # 编译工作流
        compiled_graph = workflow.compile()
        initial_state = workflow.get_initial_state(test_query, max_iterations=2)
        
        # 执行工作流
        result = await compiled_graph.ainvoke(initial_state)
        
        print("工作流执行完成!")
        print(f"最终消息数量: {len(result.get('messages', []))}")
        if result.get('messages'):
            final_message = result['messages'][-1]
            print(f"最终回复长度: {len(final_message.content)} 字符")
            print(f"最终回复预览: {final_message.content[:200]}...")
        
    except Exception as e:
        print(f"工作流执行失败: {e}")
        import traceback
        traceback.print_exc()
    
    print()


async def main():
    """主测试函数"""
    print("开始测试优化后的工作流框架...")
    print("=" * 50)
    
    try:
        await test_work_level_planner()
        await test_task_level_planner()
        await test_iteration_decider()
        await test_complete_workflow()
        
        print("=" * 50)
        print("所有测试完成!")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
