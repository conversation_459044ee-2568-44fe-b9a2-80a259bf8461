#!/usr/bin/env python3
"""
简单测试工具描述生成功能
"""

# 模拟工具注册表
class MockToolRegistry:
    def __init__(self):
        self._tools = {}
    
    def register(self, tool_type: str, executor):
        self._tools[tool_type] = executor
    
    def get(self, tool_type: str):
        return self._tools[tool_type]
    
    def list_tools(self):
        return list(self._tools.keys())


# 模拟查询执行器
class MockStructuredDataQueryExecutor:
    """
    结构化数据查询执行器
    
    功能描述：
    - 专门处理违规案例数据库的结构化查询
    - 适用于条件筛选、案例计数、精确信息查询
    - 支持按时间、公司、违规类型等字段进行筛选
    - 返回标准表格格式数据（最多前10条记录）
    
    适用场景：
    - 案例计数：查询满足特定条件的案例总数
    - 案例筛选：获取符合条件的案例列表  
    - 精确查询：查询处罚日期、案例进程、处罚对象等明确字段
    - 数据统计：获取罚款金额、处罚类型等数值信息
    
    限制说明：
    - 每次查询最多返回前10条数据，无法翻页
    - 不支持分组统计、聚合计算等复杂分析
    - 需要使用自然语言描述查询需求
    """
    pass


class MockVectorSearchQueryExecutor:
    """
    向量库查询执行器
    
    功能描述：
    - 基于语义相似度的违规案例文本内容搜索
    - 使用MultiQuery + Rerank技术提升检索精度
    - 支持模糊查询和内容匹配
    - 返回相关度排序的案例结果
    
    适用场景：
    - 违规手段搜索：查找具体的违规操作方式和手段
    - 内容描述搜索：基于业务场景描述查找相关案例
    - 相似案例查找：根据案例描述寻找类似违规情况
    - 探索性查询：发现未知关联和相关信息
    - 补救查询：当结构化查询失败时的备选方案
    
    技术特点：
    - 支持自然语言查询，理解语义含义
    - 多维度查询生成，提高召回率
    - Rerank重排序，提升结果相关性
    - 返回案例详情和相关度评分
    """
    pass


# 工具描述生成器
class ToolDescriptionGenerator:
    """工具描述生成器，从注册的工具中提取描述信息"""
    
    @staticmethod
    def extract_tool_description(tool_class_or_instance) -> str:
        """从工具类或实例中提取描述信息"""
        try:
            if hasattr(tool_class_or_instance, '__doc__') and tool_class_or_instance.__doc__:
                doc = tool_class_or_instance.__doc__.strip()
                lines = [line.strip() for line in doc.split('\n') if line.strip()]
                return '\n'.join(lines)
            else:
                return "暂无描述信息"
        except Exception as e:
            return f"描述信息提取失败: {e}"
    
    @staticmethod
    def generate_tools_description(tool_registry) -> str:
        """生成所有注册工具的描述信息"""
        descriptions = []
        
        for tool_type in tool_registry.list_tools():
            try:
                tool = tool_registry.get(tool_type)
                tool_desc = ToolDescriptionGenerator.extract_tool_description(tool)
                
                # 格式化工具描述
                formatted_desc = f"- **{tool_type}**: {tool_desc}"
                descriptions.append(formatted_desc)
                
            except Exception as e:
                descriptions.append(f"- **{tool_type}**: 描述生成失败: {e}")
        
        return '\n'.join(descriptions)


def test_tool_description_generation():
    """测试工具描述生成功能"""
    print("=== 测试工具描述生成功能 ===")
    
    # 创建工具注册表
    tool_registry = MockToolRegistry()
    
    # 注册工具
    tool_registry.register("structured_data", MockStructuredDataQueryExecutor())
    tool_registry.register("vector_search", MockVectorSearchQueryExecutor())
    
    # 生成工具描述
    tools_description = ToolDescriptionGenerator.generate_tools_description(tool_registry)
    
    print("生成的工具描述：")
    print(tools_description)
    print()
    
    print("=== 工具描述验证 ===")
    print(f"描述长度: {len(tools_description)}")
    print(f"包含structured_data: {'structured_data' in tools_description}")
    print(f"包含vector_search: {'vector_search' in tools_description}")
    print(f"包含功能描述: {'功能描述' in tools_description}")
    print(f"包含适用场景: {'适用场景' in tools_description}")


if __name__ == "__main__":
    test_tool_description_generation()
