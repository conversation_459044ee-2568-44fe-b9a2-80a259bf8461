#!/usr/bin/env python3
"""
测试工具描述生成功能
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sub_agents.sql_database_chain.violation_data_search_new.workflow.generic_violation_workflow import (
    ToolDescriptionGenerator,
    StructuredDataQueryExecutor,
    VectorSearchQueryExecutor,
    create_violation_planner_prompt,
    create_violation_iteration_decider_prompt
)
from sub_agents.sql_database_chain.violation_data_search_new.workflow.generic_workflow_framework import ToolRegistry


def test_tool_description_generation():
    """测试工具描述生成功能"""
    print("=== 测试工具描述生成功能 ===")
    
    # 创建工具注册表
    tool_registry = ToolRegistry()
    
    # 注册工具
    tool_registry.register("structured_data", StructuredDataQueryExecutor())
    tool_registry.register("vector_search", VectorSearchQueryExecutor())
    
    # 生成工具描述
    tools_description = ToolDescriptionGenerator.generate_tools_description(tool_registry)
    
    print("生成的工具描述：")
    print(tools_description)
    print()
    
    # 测试提示词生成
    print("=== 测试规划器提示词生成 ===")
    planner_prompt = create_violation_planner_prompt(tools_description)
    print("规划器提示词长度:", len(planner_prompt))
    print("包含工具描述:", "structured_data" in planner_prompt and "vector_search" in planner_prompt)
    print()
    
    print("=== 测试迭代决策器提示词生成 ===")
    iteration_prompt = create_violation_iteration_decider_prompt(tools_description)
    print("迭代决策器提示词长度:", len(iteration_prompt))
    print("包含工具描述:", "structured_data" in iteration_prompt and "vector_search" in iteration_prompt)
    print()
    
    # 显示工具描述的前500个字符
    print("=== 工具描述预览 ===")
    print(tools_description[:500] + "..." if len(tools_description) > 500 else tools_description)


if __name__ == "__main__":
    test_tool_description_generation()
