# 从旧框架迁移到新智能体框架指南

## 迁移概述

本指南帮助您从原有的 `GenericWorkflowFramework` 迁移到新的 `AgenticWorkflowFramework`。

## 快速迁移步骤

### 1. 替换导入

**旧代码：**
```python
from generic_workflow_framework import create_generic_workflow
```

**新代码：**
```python
from agentic_workflow_framework import create_agentic_workflow
```

### 2. 更新实例化方式

**旧代码：**
```python
workflow = create_generic_workflow()
# 需要手动注册工具
workflow.tool_registry.register("query_tool", GenericQueryExecutor())
```

**新代码：**
```python
workflow = create_agentic_workflow()
# 工具自动注册，无需手动配置
```

### 3. 简化调用方式

**旧代码：**
```python
# 复杂的状态初始化
initial_state = workflow.get_initial_state(
    message="查询违规案例",
    context={"company": "test"},
    max_iterations=5
)

# 需要编译和配置
compiled_graph = workflow.compile()
result = await compiled_graph.ainvoke(initial_state)
final_answer = result["messages"][-1].content
```

**新代码：**
```python
# 简单直接的调用
result = await workflow.run("查询违规案例", context={"company": "test"})
```

## 详细迁移对照表

### 状态字段映射

| 旧框架字段 | 新框架处理方式 | 说明 |
|-----------|---------------|------|
| `messages` | `messages` | 保持不变，仍然是核心 |
| `context` | `context` | 保持不变，可选上下文 |
| `intent_type` | 内部处理 | 由 SupervisorAgent 自动识别 |
| `user_goal` | 消息中体现 | 通过 PlannerAgent 的分析体现 |
| `approach` | 消息中体现 | 通过 PlannerAgent 的思考体现 |
| `current_tasks` | 工具调用 | 转换为 LLM 的工具调用 |
| `task_results` | ToolMessage | 工具执行结果自动添加到消息中 |
| `iteration_count` | 自动管理 | 通过消息循环自动实现 |
| `need_more_tasks` | 自动判断 | PlannerAgent 自动决定是否继续 |
| `final_result` | 最后的 AIMessage | 最终回答在消息列表中 |

### 组件映射

| 旧框架组件 | 新框架组件 | 变化说明 |
|-----------|-----------|----------|
| `GenericSupervisorAgent` | `SupervisorAgent` | 简化为意图识别 |
| `HumanLikePlanner` | `PlannerAgent` | 增强为工具调用决策 |
| `GenericQueryExecutor` | `@tool` 函数 | 转换为标准工具 |
| `IterationDecider` | PlannerAgent 内置 | 集成到规划逻辑中 |
| `GenericResultMerger` | PlannerAgent 内置 | 集成到最终回答生成中 |
| `ChartGenerator` | `generate_chart` 工具 | 转换为标准工具 |

### 节点映射

| 旧框架节点 | 新框架节点 | 说明 |
|-----------|-----------|------|
| `_supervisor_node` | `supervisor` | 功能简化 |
| `_planning_node` | `planner` | 增强为工具调用 |
| `_dispatcher_node` + `_task_worker_node` | `tools` (ToolNode) | 自动并行执行 |
| `_iteration_check_node` | planner ⟷ tools 循环 | 自动迭代 |
| `_aggregation_node` | PlannerAgent 最终回答 | 集成处理 |
| `_charts_node` | `generate_chart` 工具 | 按需调用 |

## 功能对比

### 工具执行

**旧框架：**
```python
# 手动工具注册和执行
class GenericQueryExecutor:
    async def execute(self, task: Dict[str, Any], context: Dict[str, Any] = None) -> str:
        query = task.get("query", "")
        result = await execute_multi_query_rerank_search(query)
        return str(result)

# 在节点中手动调用
tool_type = task.get('tool_type', 'query_tool')
executor = self.tool_registry.get(tool_type)
result = await executor.execute(task, context)
```

**新框架：**
```python
# 自动工具调用
@tool
async def vector_database_search(query: str) -> dict:
    """在违规案例向量数据库中进行搜索"""
    result = await execute_multi_query_rerank_search(query)
    return result

# LLM 自动决定调用
# 无需手动编写调用逻辑
```

### 迭代控制

**旧框架：**
```python
# 复杂的迭代决策逻辑
class IterationDecider:
    async def decide(self, user_goal, approach, current_results, messages, need_chart):
        # 复杂的判断逻辑
        if 需要更多信息:
            return {"need_more_tasks": True, "new_tasks": [...]}
        else:
            return {"need_more_tasks": False}
```

**新框架：**
```python
# 自动迭代，PlannerAgent 自主决定
# 如果需要更多信息，自动调用更多工具
# 如果信息足够，生成最终回答
```

## 迁移检查清单

### ✅ 必须完成的迁移

- [ ] 更新导入语句
- [ ] 替换工作流创建方式
- [ ] 简化调用代码
- [ ] 移除手动工具注册代码
- [ ] 更新错误处理逻辑

### ✅ 可选的优化

- [ ] 优化提示词以适应新的工具调用模式
- [ ] 添加新的工具函数
- [ ] 自定义 Agent 行为
- [ ] 集成流式处理

### ✅ 测试验证

- [ ] 运行基础功能测试
- [ ] 验证工具调用正确性
- [ ] 检查性能表现
- [ ] 确认错误处理

## 常见问题解答

### Q: 新框架是否支持原有的所有功能？
A: 是的，新框架支持所有原有功能，并且提供了更好的扩展性和灵活性。

### Q: 迁移后性能会有影响吗？
A: 新框架通过自动并行工具调用和简化的状态管理，通常性能会有所提升。

### Q: 如何添加自定义工具？
A: 使用 `@tool` 装饰器定义新工具，然后添加到工具列表中即可。

### Q: 是否需要重写所有的提示词？
A: 不需要完全重写，但建议针对工具调用模式进行优化。

### Q: 新框架是否向后兼容？
A: API 层面不完全兼容，但功能完全覆盖，迁移成本较低。

## 迁移示例

### 完整的迁移示例

**旧代码：**
```python
# 创建工作流
workflow = create_generic_workflow()
workflow.tool_registry.register("query_tool", GenericQueryExecutor())

# 复杂调用
initial_state = workflow.get_initial_state(
    message="查询违规案例",
    context={"company": "test"},
    max_iterations=5
)
compiled_graph = workflow.compile()
result = await compiled_graph.ainvoke(initial_state)
answer = result["messages"][-1].content
```

**新代码：**
```python
# 创建工作流
workflow = create_agentic_workflow()

# 简单调用
answer = await workflow.run("查询违规案例", context={"company": "test"})
```

通过以上迁移，您可以享受到新框架带来的所有优势：更简洁的代码、更强的扩展性、更智能的工具调用。
