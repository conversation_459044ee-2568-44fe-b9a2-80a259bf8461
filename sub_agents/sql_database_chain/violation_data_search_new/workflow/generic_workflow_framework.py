"""
通用多Agent工作流框架
核心流程：监督者Agent → 快速规划器 → 任务分发器 → 工作节点 → 迭代检查 → 结果合并器
模拟人类解决问题的思维过程：理解问题 → 快速规划 → 并行执行 → 边做边想 → 整合回答
完全业务无关的通用框架，内置通用提示词，可直接运行
"""
import json
import operator
from typing import Annotated, Optional, Dict, Any, List, Coroutine, Union

from langchain_core.messages import AnyMessage, AIMessage, ToolMessage
from langchain_core.output_parsers import PydanticOutputParser
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.tools import tool
from langgraph.config import get_stream_writer
from langgraph.graph import StateGraph, START, END, add_messages
from langgraph.prebuilt import ToolNode
from langgraph.types import Send
from pydantic import BaseModel, Field
from typing_extensions import Literal
from typing_extensions import TypedDict

from sub_agents.rep.utils.llm_utils import get_a_llm
from sub_agents.sql_database_chain.violation_data_search_new.core.vector_search import execute_multi_query_rerank_search
from utils.load_env import logger


# ============================================================================
# 通用工具定义 - 使用 @tool 装饰器
# ============================================================================

@tool
async def tool_a(query: str) -> str:
    """
    通用工具A - 用于处理A类型的查询任务

    Args:
        query: 查询内容描述

    Returns:
        查询结果字符串
    """
    # 这里是工具A的实际实现，暂时返回模拟结果
    logger.info(f"[ToolA] 执行查询: {query}")
    return f"工具A执行结果: {query}"


@tool
async def tool_b(query: str) -> str:
    """
    通用工具B - 用于处理B类型的查询任务

    Args:
        query: 查询内容描述

    Returns:
        查询结果字符串
    """
    # 这里是工具B的实际实现，暂时返回模拟结果
    logger.info(f"[ToolB] 执行查询: {query}")
    return f"工具B执行结果: {query}"


# ============================================================================
# 状态定义和工具函数
# ============================================================================

# 自定义 reducer，用于清空任务结果
def update_task_results(
        existing: Optional[list] = None,
        updates: Optional[Union[list, Literal["clear"]]] = None,
) -> List[Dict[str, Any]]:
    if existing is None:
        existing = []
    if updates is None:
        return existing
    if updates == "clear":
        return []
    return existing + updates


# 通用状态定义
class GenericWorkflowState(TypedDict):
    """人类化工作流状态"""
    messages: Annotated[list[AnyMessage], add_messages]
    context: Optional[Dict[str, Any]]  # 业务上下文

    # 核心字段
    intent_type: Optional[str]  # 意图类型
    user_goal: Optional[str]  # 用户真实目标
    approach: Optional[str]  # 解决思路
    need_chart: Optional[bool]  # 是否需要生成图表
    need_character_relationships: Optional[bool]  # 是否需要生成违规案例人物关系图

    # 任务执行
    current_tasks: Optional[List[Dict[str, Any]]]  # 当前任务列表
    task_results: Annotated[List[Dict[str, Any]], update_task_results]  # 任务结果
    iteration_count: Optional[int]  # 迭代次数
    max_iterations: Optional[int]  # 最大迭代次数

    # 迭代控制
    need_more_tasks: Optional[bool]  # 是否需要更多任务
    new_iteration_tasks: Optional[List[Dict[str, Any]]]  # 新的迭代任务

    final_result: Optional[str]  # 最终结果


# 单个任务状态
class TaskState(TypedDict):
    """单个任务状态 - 参考官方 WorkerState 模式"""
    task: Dict[str, Any]  # 任务信息
    context: Optional[Dict[str, Any]]  # 业务上下文
    task_results: Annotated[List[Dict[str, Any]], update_task_results]


# Pydantic模型
class Task(BaseModel):
    """人类化任务模型"""
    id: str = Field(description="任务ID")
    description: str = Field(description="任务描述")
    query: str = Field(description="查询内容")
    tool_type: str = Field(description="工具类型")
    intent: str = Field(description="任务意图：为了什么目的做这个任务")


class PlanningResult(BaseModel):
    """快速规划结果"""
    user_goal: str = Field(description="用户的真实目标")
    approach: str = Field(description="解决思路")
    initial_tasks: List[Task] = Field(description="初始任务列表")
    need_chart: bool = Field(description="是否需要生成图表")
    need_character_relationships: bool = Field(description="是否需要生成违规案例人物关系图")


class IntentClassificationResult(BaseModel):
    """意图分类结果"""
    intent_type: str = Field(description="意图类型")
    confidence: float = Field(description="置信度")
    response: Optional[str] = Field(description="直接回复内容（如果适用）")


class IterationDecision(BaseModel):
    """迭代决策结果"""
    need_more_tasks: bool = Field(description="是否需要更多任务")
    new_tasks: List[Task] = Field(description="新增任务列表")
    reasoning: str = Field(description="决策理由")


# 简单对话回复生成器
class SimpleResponseGenerator:
    """专门处理简单对话的回复生成器"""

    def __init__(self, llm):
        self.llm = llm

        self.prompt = ChatPromptTemplate.from_messages([
            ("system", """你是一个专业、友好的违规案例查询助手。用户刚才发送了简单的对话内容，请生成一个合适的回复。

你的特点：
- 专业但亲切，不过分正式
- 主动介绍你的能力（数据查询、分析等）
- 引导用户提出具体问题
- 回复简洁明了，不要太长

根据用户输入类型生成回复：
- 问候语：友好回应并介绍能力
- 感谢语：谦逊回应并提供进一步帮助
- 测试输入：确认系统正常并介绍功能
- 闲聊内容：礼貌回应并引导到专业话题
- 其他：提供友好的默认回复

请直接返回回复内容，不要添加额外格式。"""),
            ("user", "{user_input}")
        ])

    async def generate_response(self, user_input: str) -> str:
        """生成简单对话回复"""
        try:
            chain = self.prompt | self.llm
            response = await chain.ainvoke({"user_input": user_input})
            return response.content
        except Exception as e:
            logger.warning(f"简单回复生成失败，使用默认回复: {e}")
            # 降级处理
            return "您好！我是智能助手，可以帮您查询和分析各种信息。请问有什么可以帮助您的吗？"


class ChartGenerator:
    """图表可视化数据生成器"""

    def __init__(self, llm):
        self.llm = llm

        self.prompt = ChatPromptTemplate.from_messages([
            ("system", """你是专业的违规案例总结智能助手，负责根据当前的查询结果和数据，选取合适的echarts图表类型，生成对应结构的json数据。

你需要：
1. **深度分析、理解违规案例内容**：{final_results}
2. **选择合适的chart图表类型**
3. **确保生成的json对象格式符合echarts.js库，确保生成的图表内容为中文**

## 输出格式

**直接输出json对象，不需要回答额外的内容**，如果分析出多个json对象，直接将它们以下面格式输出：

```json 
 对象1 
```
```json 
 对象2 
```

## 图表类型

### 折线图/柱状图/饼图等

根据总结内容和查询结果，以给定数据结构构建一个或多个能够被echarts库直接渲染的json对象，图表类型由你来决定。

基本的echarts图表结构示例如下：

{{
    id: '10001',
    name: 'xx情况折线图',
    type: 'xxx',
    option: {{
        legend: {{}},
        tooltip: {{}},
        dataset: {{  // 必须使用dataset
            source: [
              ['product', '2015', '2016', '2017'],
              ['Matcha Latte', 43.3, 85.8, 93.7],
              ['Milk Tea', 83.1, 73.4, 55.1],
              ['Cheese Cocoa', 86.4, 65.2, 82.5],
              ['Walnut Brownie', 72.4, 53.9, 39.1]
           ]
        }},
        xAxis: {{ type: 'category' }},
        yAxis: {{}},
        series: [{{ type: 'line' }}, {{ type: 'bar' }}, {{ type: 'pie' }}]
    }};
}}

## 其他注意事项

- 不要生成 type 为 table 的图表
- 必须严格使用数据集dataset这个属性

"""),
            ("user", "{final_results}")
        ])

    async def generate_response(self, task_results: list, messages: list) -> str:
        """生成可视化图表回复"""
        try:
            # 合并任务结果为字符串
            final_results = "\n".join([str(result) for result in task_results])
            chain = self.prompt | self.llm
            response = await chain.ainvoke({
                "final_results": final_results,
                "messages": messages
            })
            return response.content
        except Exception as e:
            logger.warning(f"生成可视化图表数据失败，使用默认回复: {e}")
            # 降级处理
            return "您好！我是智能助手，可以帮您查询和分析各种信息。请问有什么可以帮助您的吗？"


class CharacterRelationshipsGenerator:
    """违规案例人物关系图生成器"""

    def __init__(self, llm):
        self.llm = llm

        self.prompt = ChatPromptTemplate.from_messages([
            ("system", """你是专业的违规案例总结智能助手，负责根据当前的违规案例查询结果和数据，提取重要信息，以 mermaid 语法生成该违规案例事件中的人物关系图。

    ## 任务说明
    你需要：
    1. **深度分析、理解违规案例内容，如果出现多个违规案例，则选择与用户提问最贴切的一个，其余忽略**：{final_results}
    2. **提取重要信息，简明总结出案例关系中的时间、主体、角色、关键行为**
        注意，每一组重要信息应该包含以下四个方面：
        1. 时间
        2. 主体
        3. 角色
        4. 关键行为
        然后按照关键行为发生的时间和发生的关联性排列在一个表格中，表格示例如下：
        
        | 时间     | 主体 | 角色           | 关键行为           |
        | -------- | ---- | -------------- | ------------------ |
        | 2025.1.2 | 张三 | 上市公司董事长 | 向李四传递内幕信息 |
        | 2025.2.5 | 李四 | 张三同学       | 买卖上市公司股票   |
        | ...      | ...  | ...            | ...                |

    3. **根据提取的多组重要信息，结合时间、主体、角色、关键行为，以 mermaid 语法生成该违规案例事件中一个关联关系图谱穿透结构图**，生成步骤如下：
        ① 首先将各主体的名称和角色标注好；
        ② 根据关键行为，用线条将主体与主体之间连接起来（生成示例如下）：
        
        ```mermaid
        graph TD
            A --> B[关联方2：湖北庆久建筑工程有限公司]
            A -.->|建筑装修交易<br>2021.11-2024.12<br>金额：1655万元| B
            A --> C
            C[实际控制人/董事长：刘志禾] -->|知悉未披露（违反《上市规则》多条）| A
            A --> D
            D[董事会秘书：邵娜] --> |知悉未披露（违反《上市规则》多条）| A
            A[苏州华源控股股份有限公司] --> E[子公司：苏州华源中邮包装有限公司]
            A[苏州华源控股股份有限公司] --> F[关联方1：苏州昌尊贸易有限公司]
            E -.-> |汽车租赁交易（2020.9-2023.3）金额：43.2万元| F[关联方1：苏州昌尊贸易有限公司]
            
            style B fill:#f9f,stroke:#333
            style C fill:#bbf,stroke:#333
        ```
        其中，[]由角色＋主体描述，||由时间+关键行为描述，style可以用来标注最关键的内容，注意-->和-.->的使用应该合理
    
    ## 输出格式
    
    根据xxxx违规案例，进行人物关系穿透分析。
    
    以下是按照时间顺序梳理的xxxx案例核心主体，角色及关键行为的表格：
    
    | 时间     | 主体 | 角色           | 关键行为           |
    | -------- | ---- | -------------- | ------------------ |
    | 2025.1.2 | 张三 | 上市公司董事长 | 向李四传递内幕信息 |
    | 2025.2.5 | 李四 | 张三同学       | 买卖上市公司股票   |
    | ...      | ...  | ...            | ...                |
    
    关键行为逻辑链：
    1.内幕信息形成与传递：
        韩某（中介）→邵某超（董秘）→郑某昌（董事长）→唐政斌（同学）。
        唐政斌与郑某昌联系时间与股票买卖高度吻合。
    2.交易异常性：
        唐政斌控制账户资金来源集中（转入610万元），买入时点紧贴谈判关键节点（如4月9日信息形成后即买入，5月12日谈判后大额转入资金）。复牌后迅速卖出，资金回流，交易目的明确。
    3.监管认定要点：
        内幕信息形成于2014年4月9日（郑某昌知悉收购意向），公开于5月27日（停牌公告）。唐政斌作为郑某昌密切联系人，交易行为与内幕信息高度相关，构成内幕交易。

    根据上述表格，生成一个内幕信息传递关系图谱穿透，
    以下是基于案例的内幕信息传递关系图谱穿透（以箭头表示信息流向，角色标注在主体旁）：
    
    ```mermaid
        graph TD
        关系图
    ```
    
    ## 注意事项
    - mermaid语法中[]以及||中不能出现中文符号，包括中文冒号、逗号、括号等，这些中文符号一律使用空格代替
    - mermaid语法中[]以及||中不能出现英文括号，英文括号一律使用空格代替
    """),
            ("user", "{messages}")
        ])

    async def generate_response(self, task_results: str, messages: list) -> str:
        """生成人物关系图回复"""
        try:
            # 合并任务结果为字符串
            chain = self.prompt | self.llm
            response = await chain.ainvoke({
                "final_results": task_results,
                "messages": messages
            })
            return response.content
        except Exception as e:
            logger.warning(f"生成人物关系图数据失败，使用默认回复: {e}")
            # 降级处理
            return "您好！我是智能助手，可以帮您查询和分析各种信息。请问有什么可以帮助您的吗？"


# 通用监督者Agent
class GenericSupervisorAgent:
    """通用监督者Agent，内置通用提示词"""

    def __init__(self, llm):
        self.llm = llm
        self.parser = PydanticOutputParser(pydantic_object=IntentClassificationResult)

        self.prompt = ChatPromptTemplate.from_messages([
            ("system", """你是一个智能助手的监督者，负责识别用户意图并决定处理路径。

你需要判断用户输入属于以下哪种类型：

1. **simple_chat（简单对话）**：
   - 问候语：你好、hi、hello等
   - 感谢语：谢谢、感谢等
   - 闲聊：天气、心情、无关话题等
   - 测试性输入：测试、test等
   - 无意义输入：随机字符、表情符号等

2. **query_task（查询任务）**：
   - 任何需要查询、搜索、分析的请求
   - 数据查询、信息检索、内容搜索
   - 统计分析、对比分析等

{format_instructions}

注意：你只需要进行分类，不需要生成回复内容。response字段始终为null。

示例：
用户："你好"
输出：{{"intent_type": "simple_chat", "confidence": 0.95, "response": null}}

用户："查询用户数据"
输出：{{"intent_type": "query_task", "confidence": 0.98, "response": null}}"""),
            ("placeholder", "{messages}"),
        ])

    async def classify_intent(self, messages: List[AnyMessage]) -> Dict[str, Any]:
        """分类用户意图"""
        try:
            chain = self.prompt | self.llm | self.parser
            result = await chain.ainvoke({
                "messages": messages,
                "format_instructions": self.parser.get_format_instructions()
            })
            logger.info(f"[SupervisorAgent] 意图识别完成: {result.model_dump()}")
            return result.model_dump()
        except Exception as e:
            # 降级处理：默认为查询任务
            logger.info(f"[WARNING] 意图识别失败，使用默认策略: {e}")
            return {
                "intent_type": "query_task",
                "confidence": 0.5,
                "response": None
            }


# 基于 Function Call 的快速规划器
class FunctionCallPlanner:
    """使用 Function Call 的智能规划器"""

    def __init__(self, llm, tools):
        self.tools = tools
        # 绑定工具到 LLM
        self.llm_with_tools = llm.bind_tools(tools)

        self.prompt = ChatPromptTemplate.from_messages([
            ("system", """你是智能规划助手，负责分析用户需求并选择合适的工具来解决问题。

你的工作流程：
1. **理解用户需求** - 分析用户真正想要什么
2. **选择合适工具** - 根据需求选择最合适的工具
3. **制定执行策略** - 决定工具调用的顺序和参数

可用工具：
- tool_a: 用于处理A类型的查询任务
- tool_b: 用于处理B类型的查询任务

工作原则：
- 优先选择最直接有效的工具
- 如果需要多个信息源，可以调用多个工具
- 根据用户问题的复杂程度决定工具调用策略
- 始终以解决用户问题为目标

请根据用户输入，智能选择并调用合适的工具。如果不需要调用工具，直接回答用户问题。"""),
            ("placeholder", "{messages}"),
        ])

    async def plan(self, messages: List[AnyMessage]) -> AIMessage:
        """使用 Function Call 进行智能规划"""
        try:
            chain = self.prompt | self.llm_with_tools
            result = await chain.ainvoke({"messages": messages})
            logger.info(f"[FunctionCallPlanner] 规划完成，工具调用数: {len(getattr(result, 'tool_calls', []))}")
            return result
        except Exception as e:
            logger.error(f"[FunctionCallPlanner] 规划失败: {e}")
            # 降级处理：返回简单回复
            return AIMessage(content=f"抱歉，处理您的请求时出现了问题：{str(e)}")


# 基于 Function Call 的迭代检查器
class FunctionCallIterationChecker:
    """使用 Function Call 的智能迭代检查器"""

    def __init__(self, llm, tools):
        self.tools = tools
        # 绑定工具到 LLM
        self.llm_with_tools = llm.bind_tools(tools)

        self.prompt = ChatPromptTemplate.from_messages([
            ("system", """你是智能迭代检查助手，负责分析当前查询结果，判断是否需要更多信息来完整回答用户问题。

你的职责：
1. **分析当前结果** - 评估已有信息是否足够
2. **识别信息缺口** - 发现还需要什么信息
3. **决定下一步行动** - 选择合适的工具获取补充信息，或者结束查询

决策原则：
- 如果当前信息已经足够回答用户问题，直接总结回答，不要调用工具
- 如果发现明显的信息缺口，选择合适的工具获取补充信息
- 避免重复查询相同或相似的信息
- 最多进行3轮补充查询，避免无限循环

可用工具：
- tool_a: 用于处理A类型的查询任务
- tool_b: 用于处理B类型的查询任务

当前查询结果：
{current_results}

用户原始问题：{user_question}

请分析是否需要更多信息，如果需要请调用合适的工具，如果不需要请直接总结回答用户问题。"""),
            ("placeholder", "{messages}"),
        ])

    async def check_and_decide(self, messages: List[AnyMessage], current_results: str, user_question: str) -> AIMessage:
        """检查当前结果并决定下一步行动"""
        try:
            chain = self.prompt | self.llm_with_tools
            result = await chain.ainvoke({
                "messages": messages,
                "current_results": current_results,
                "user_question": user_question
            })

            tool_calls_count = len(getattr(result, 'tool_calls', []))
            if tool_calls_count > 0:
                logger.info(f"[FunctionCallIterationChecker] 决定继续查询，工具调用数: {tool_calls_count}")
            else:
                logger.info(f"[FunctionCallIterationChecker] 决定结束查询，开始总结回答")

            return result
        except Exception as e:
            logger.error(f"[FunctionCallIterationChecker] 检查失败: {e}")
            # 降级处理：直接结束迭代
            return AIMessage(content=f"根据当前查询结果：\n{current_results}")


# 通用结果合并器
class GenericResultMerger:
    """通用结果合并器，内置通用提示词"""

    def __init__(self, llm):
        self.llm = llm

        self.prompt = ChatPromptTemplate.from_messages([
            ("system", """你是专业的智能助手，基于查询结果为用户提供准确、清晰的回答。

你的任务是基于查询结果，直接回答用户的问题。注意：
1. 用专业但友好的语言回答
2. 直接回应用户关心的核心问题
3. 如果是对比查询，重点分析差异和趋势
4. 如果是统计查询，提供关键数据并解读含义
5. 结构清晰，先总结后详述
6. 避免重复信息，突出最有价值的内容
7. 直接忽视生成图表的请求，你的回答中完全不要提及‘图表’、‘可视化’等词语，就像用户从未提出过这个要求一样。
8. 直接忽视生成违规案例人物关系图的请求，你的回答中完全不要提及相关信息，就像用户从未提出过这个要求一样。

回答风格：
- 开头直接回应用户问题："根据查询结果..."
- 用数据说话，重点解释结果反映的现象
- 语言自然流畅，避免生硬的技术术语
- 如果发现重要信息，主动指出

重要限制：
- 只基于查询结果回答，不要提出超出数据范围的建议
- 专注于解读现有数据

## 查询结果：
{results}"""),
            ("placeholder", "{messages}")
        ])

    async def merge(self, task_results: List[Dict[str, Any]], messages: List[AnyMessage]) -> str:
        """基于查询结果生成用户友好的回答"""
        try:
            # 整理结果
            results_text = ""
            for i, task_result in enumerate(task_results, 1):
                task_desc = task_result.get('description', f'查询{i}')
                task_content = task_result.get('result', '')
                results_text += f"{task_desc}：\n{task_content}\n\n"

            chain = self.prompt | self.llm
            response = await chain.ainvoke({
                "messages": messages,
                "results": results_text.strip()
            })

            return response.content

        except Exception as e:
            # 降级处理：简单拼接结果
            if len(task_results) == 1:
                return f"根据查询结果：\n{task_results[0].get('result', '')}"
            else:
                results = []
                for i, r in enumerate(task_results, 1):
                    results.append(f"结果{i}：{r.get('result', '')}")
                return "\n\n".join(results)


# 通用查询执行器
class GenericQueryExecutor:
    """通用查询执行器，向量库执行"""

    async def execute(self, task: Dict[str, Any], context: Dict[str, Any] = None) -> str:
        """执行查询任务"""
        query = task.get("query", "")
        description = task.get("description", "")

        # 调用向量库查询函数
        result_dict = await execute_multi_query_rerank_search(
            query
        )

        # 将字典结果转换为字符串格式
        if isinstance(result_dict, dict):
            search_summary = result_dict.get("search_summary", "")
            formatted_results = result_dict.get("formatted_results", [])

            # 将结构化数据转换为字符串格式
            result_strings = []
            for case in formatted_results:
                if isinstance(case, dict):
                    case_str = f"""案例{case.get('case_number', 'N/A')}：
                            - 案例标题: {case.get('title', 'N/A')}
                            - 公司名称: {case.get('company_name', 'N/A')} ({case.get('company_code', 'N/A')})
                            - 处罚机构: {case.get('punish_org', 'N/A')}
                            - 违规事项: {case.get('case_list', 'N/A')}
                            - 事件时间: {case.get('event_time', 'N/A')}
                            - Rerank得分: {case.get('relevance_score', 0)}"""
                    if case.get('content_preview'):
                        case_str += f"\n- 相关内容: {case.get('content_preview')}"
                    result_strings.append(case_str)
                else:
                    # 兼容旧的字符串格式
                    result_strings.append(str(case))

            result = search_summary + "\n\n" + "\n\n".join(result_strings)
        else:
            result = str(result_dict)

        return result


# 工具注册表
class ToolRegistry:
    """工具注册表"""

    def __init__(self):
        self._tools: Dict[str, Any] = {}

    def register(self, tool_type: str, executor: Any):
        """注册工具执行器"""
        self._tools[tool_type] = executor

    def get(self, tool_type: str) -> Any:
        """获取工具执行器"""
        if tool_type not in self._tools:
            raise ValueError(f"未注册的工具类型: {tool_type}")
        return self._tools[tool_type]

    def list_tools(self) -> List[str]:
        """列出所有工具类型"""
        return list(self._tools.keys())


# 通用工作流框架
class GenericWorkflowFramework:
    """通用工作流框架，内置所有组件，可直接运行"""

    def __init__(self, llm):
        self.llm = llm

        # 初始化内置组件
        self.supervisor = GenericSupervisorAgent(llm)
        self.simple_response_generator = SimpleResponseGenerator(llm)
        self.planner = HumanLikePlanner(llm)
        self.iteration_decider = IterationDecider(llm)
        self.chart_generator = ChartGenerator(llm)
        self.character_relationships_generator = CharacterRelationshipsGenerator(llm)
        self.merger = GenericResultMerger(llm)

        # 初始化工具注册表并注册默认工具
        self.tool_registry = ToolRegistry()
        # self.tool_registry.register("query_tool", GenericQueryExecutor())

        # 构建工作流图（不编译，留给用户自定义编译）
        self.graph = self._build_graph()

    def _build_graph(self) -> StateGraph:
        """构建人类化工作流图"""
        graph = StateGraph(GenericWorkflowState)

        # 添加节点
        graph.add_node("supervisor", self._supervisor_node)  # 1. 理解问题
        graph.add_node("simple_response", self._simple_response_node)
        graph.add_node("planning", self._planning_node)  # 2. 快速规划
        graph.add_node("dispatcher", self._dispatcher_node)  # 3. 并行执行
        graph.add_node("task_worker", self._task_worker_node)
        graph.add_node("iteration_check", self._iteration_check_node)  # 4. 边做边想
        graph.add_node("aggregation", self._aggregation_node)  # 5. 整合回答
        graph.add_node("generate_chart", self._charts_node)  # 6. 生成可视化图表
        graph.add_node("generate_character_relationships", self._character_relationships_node)  # 7. 生成人物关系图
        graph.add_node("final_merge", self._final_merge_node)  # 新增汇聚节点

        # 设置流程
        graph.add_edge(START, "supervisor")

        # 监督者路由 - 使用类型安全的路由函数
        def supervisor_router(state: GenericWorkflowState) -> Literal["simple_response", "planning"]:
            """类型安全的监督者路由"""
            return "simple_response" if state.get("intent_type") == "simple_chat" else "planning"

        graph.add_conditional_edges(
            "supervisor",
            supervisor_router,
            {
                "simple_response": "simple_response",
                "planning": "planning"
            }
        )

        graph.add_edge("simple_response", END)

        # 规划后执行任务
        graph.add_conditional_edges(
            "planning",
            lambda state: "dispatcher" if state.get("current_tasks") else "aggregation",
            {
                "dispatcher": "dispatcher",
                "aggregation": "aggregation"
            }
        )

        # 任务执行和迭代
        graph.add_conditional_edges("dispatcher", self._dispatcher_router)
        graph.add_edge("task_worker", "iteration_check")

        # 迭代决策 - iteration_check 节点使用条件边处理动态路由
        graph.add_conditional_edges("iteration_check", self._iteration_check_router)

        graph.add_edge("aggregation", "final_merge")
        graph.add_edge("generate_chart", "final_merge")
        graph.add_edge("generate_character_relationships", "final_merge")
        graph.add_edge("final_merge", END)

        return graph

    async def _supervisor_node(self, state: GenericWorkflowState) -> Dict[str, Any]:
        """监督者节点 - 理解问题并分类意图"""
        # 发送流程信息
        try:
            writer = get_stream_writer()
            writer(json.dumps({"AI_AGENT_FLOW": "🤔 算算正在理解您的需求..."}, ensure_ascii=False))
        except Exception as e:
            logger.warning(f"发送流程信息失败: {e}")

        messages = state.get("messages", [])

        result = await self.supervisor.classify_intent(messages)

        return {
            "intent_type": result.get("intent_type"),
            "task_results": []  # 使用空列表清空，因为现在使用 operator.add
        }

    async def _simple_response_node(self, state: GenericWorkflowState) -> Dict[str, Any]:
        """简单回复节点 - 处理简单对话"""
        messages = state.get("messages", [])

        # 获取用户最后一条消息
        user_input = ""
        if messages:
            user_input = str(messages[-1].content)

        # 使用LLM生成智能回复
        response = await self.simple_response_generator.generate_response(user_input)

        return {"messages": [AIMessage(content=response)]}

    async def _planning_node(self, state: GenericWorkflowState) -> Dict[str, Any]:
        """快速规划节点 - 模拟人类的快速规划过程"""
        # 发送流程信息
        try:
            writer = get_stream_writer()
            writer(json.dumps({"AI_AGENT_FLOW": "🧠 算算正在制定最佳查询策略..."}, ensure_ascii=False))
        except Exception as e:
            logger.warning(f"发送流程信息失败: {e}")

        messages = state.get("messages", [])

        if not messages:
            return {"current_tasks": [], "task_results": []}

        last_message = messages[-1]
        result = await self.planner.plan(last_message)

        # 发送任务数量信息
        try:
            task_count = len(result.get("initial_tasks", []))
            writer = get_stream_writer()
            writer(json.dumps({"AI_AGENT_FLOW": f"🚀 启动{task_count}个智能搜索引擎，全面检索中..."}, ensure_ascii=False))
        except Exception as e:
            logger.warning(f"发送流程信息失败: {e}")

        return {
            "user_goal": result.get("user_goal"),
            "approach": result.get("approach"),
            "current_tasks": result.get("initial_tasks", []),
            "task_results": [],
            "iteration_count": 0,
            "need_chart": result.get("need_chart"),
            "need_character_relationships": result.get("need_character_relationships")
        }

    def _dispatcher_node(self, state: GenericWorkflowState) -> Dict[str, Any]:
        """任务分发节点 - 并行执行能同时做的任务"""
        current_tasks = state.get("current_tasks", [])
        return {} if current_tasks else {"task_results": []}

    def _dispatcher_router(self, state: GenericWorkflowState):
        """任务分发路由 - 并行执行当前任务"""
        current_tasks = state.get("current_tasks", [])
        if not current_tasks:
            return []

        return [
            Send("task_worker", {
                "task": task,
                "context": state.get("context"),
                "task_results": []
            })
            for task in current_tasks
        ]

    async def _task_worker_node(self, state: TaskState) -> Dict[str, Any]:
        """任务工作节点 - 执行具体任务"""
        task = state.get("task", {})
        context = state.get("context", {})

        try:
            tool_type = task.get('tool_type', 'query_tool')
            executor = self.tool_registry.get(tool_type)

            result = await executor.execute(task, context)

            task_result = {
                "task_id": task["id"],
                "description": task["description"],
                "tool_type": tool_type,
                "result": result,
                "status": "completed"
            }

            return {"task_results": [task_result]}

        except Exception as e:
            task_result = {
                "task_id": task.get("id", "unknown"),
                "description": task.get("description", "未知任务"),
                "tool_type": task.get('tool_type', 'query_tool'),
                "result": f"执行失败: {str(e)}",
                "status": "failed"
            }

            return {"task_results": [task_result]}

    def _iteration_check_router(self, state: GenericWorkflowState):
        """迭代检查路由器 - 根据状态决定下一步"""
        # 检查是否需要更多任务
        if state.get("need_more_tasks", False):
            new_tasks = state.get("new_iteration_tasks", [])
            if new_tasks:
                return [
                    Send("dispatcher", {
                        **state,
                        "current_tasks": new_tasks,
                        "iteration_count": state.get("iteration_count", 0) + 1,
                        "need_more_tasks": False,
                        "new_iteration_tasks": []
                    })
                ]

        # 由AI自主判断是否需要生成图表
        need_chart = state.get("need_chart", False)
        # 判断任务数是否小于3，如果小于3则不进行图表生成
        current_tasks = state.get("current_tasks", [])

        if len(current_tasks) < 3:
            need_chart = False
            # 如果用户明确说明需要图表
            user_query = state.get("messages", [])[0].content.lower() if (
                    state.get("messages") and len(state.get("messages")) > 0) else ""
            if any(keyword in user_query for keyword in ["图表", "可视化", "图形", "chart", "graph"]):
                need_chart = True

        # 由AI自主判断是否需要生成人物关系图
        need_character_relationships = state.get("need_character_relationships", False)

        if need_chart:
            # 并行发送到对应节点
            if need_character_relationships:
                return [
                    Send("aggregation", state),
                    Send("generate_chart", state),
                    Send("generate_character_relationships", state)
                ]
            else:
                return [
                    Send("aggregation", state),
                    Send("generate_chart", state)
                ]
        else:
            # 并行发送到对应节点
            if need_character_relationships:
                return [
                    Send("aggregation", state),
                    Send("generate_character_relationships", state)
                ]
            else:
                return [Send("aggregation", state)]

    async def _iteration_check_node(self, state: GenericWorkflowState) -> Dict[str, Any]:
        """迭代检查节点 - 边做边想，根据结果调整思路"""
        # 发送流程信息
        try:
            writer = get_stream_writer()
            writer(json.dumps({"AI_AGENT_FLOW": "📊 算算正在深度分析搜索结果..."}, ensure_ascii=False))
        except Exception as e:
            logger.warning(f"发送流程信息失败: {e}")

        task_results = state.get("task_results", [])
        iteration_count = state.get("iteration_count", 0)
        max_iterations = state.get("max_iterations", 5)
        user_goal = state.get("user_goal", "")
        approach = state.get("approach", "")
        messages = state.get("messages", [])

        # 检查是否达到最大迭代次数
        if iteration_count >= max_iterations:
            try:
                writer = get_stream_writer()
                writer(json.dumps({"AI_AGENT_FLOW": "📝 搜索完成，算算正在整理专业报告..."}, ensure_ascii=False))
            except Exception as e:
                logger.warning(f"发送流程信息失败: {e}")
            return {
                "need_more_tasks": False,
                "new_iteration_tasks": []
            }

        # 如果没有结果，停止迭代
        if not task_results:
            try:
                writer = get_stream_writer()
                writer(json.dumps({"AI_AGENT_FLOW": "📝 搜索完成，算算正在整理专业报告..."}, ensure_ascii=False))
            except Exception as e:
                logger.warning(f"发送流程信息失败: {e}")
            return {
                "need_more_tasks": False,
                "new_iteration_tasks": []
            }

        # 使用迭代决策器判断是否需要更多任务
        decision = await self.iteration_decider.decide(user_goal, approach, task_results, messages, need_chart=False,
                                                       need_character_relationships=False)

        if decision.get("need_more_tasks", False):
            new_tasks = decision.get("new_tasks", [])
            try:
                writer = get_stream_writer()
                writer(json.dumps({"AI_AGENT_FLOW": f"🔄 算算发现需要更多信息，启动{len(new_tasks)}个补充搜索..."},
                                  ensure_ascii=False))
            except Exception as e:
                logger.warning(f"发送流程信息失败: {e}")
            return {
                "need_more_tasks": True,
                "new_iteration_tasks": new_tasks
            }
        else:
            try:
                writer = get_stream_writer()
                writer(json.dumps({"AI_AGENT_FLOW": "📝 搜索完成，算算正在整理专业报告..."}, ensure_ascii=False))
            except Exception as e:
                logger.warning(f"发送流程信息失败: {e}")

            return {
                "need_more_tasks": False,
                "new_iteration_tasks": []
            }

    async def _aggregation_node(self, state: GenericWorkflowState) -> Dict[str, Any]:
        """结果汇总节点 - 整合回答"""
        task_results = state.get("task_results", [])
        messages = state.get("messages", [])

        if not task_results:
            error_msg = "抱歉，没有获取到任何查询结果"
            return {
                "messages": [AIMessage(content=error_msg)]
            }

        final_result = await self.merger.merge(task_results, messages)

        return {
            "final_result": None,
            "task_results": [],
            "messages": [AIMessage(content=final_result)]
        }

    async def _charts_node(self, state: GenericWorkflowState) -> Dict[str, Any]:
        # 将汇总结果和数据经过分析，生成指定格式的图表json
        task_results = state.get("task_results", [])
        messages = state.get("messages", [])

        try:
            writer = get_stream_writer()
            writer(json.dumps({"AI_AGENT_FLOW": "📈 汇总完成，算算正在尝试生成可视化图表..."}, ensure_ascii=False))

            # 调用AI生成图表数据
            final_result = await self.chart_generator.generate_response(task_results, messages)

            # 将AI生成结果格式化为AI_AGENT_CHART类型
            writer(json.dumps({
                "AI_AGENT_CHART": final_result
            }, ensure_ascii=False))
        except Exception as e:
            logger.warning(f"发送流程信息失败: {e}")
            final_result = str(e)

        return {
            "task_results": []
        }

    async def _character_relationships_node(self, state: GenericWorkflowState) -> dict[str, list[Any]] | None:
        # 将汇总结果和数据经过分析，生成违规案例人物关系图
        task_results = state.get("task_results", [])
        messages = state.get("messages", [])
        case_result = ''
        for task in task_results:
            # 目前只根据向量库搜索结果的违规案例进行生成
            if task["tool_type"] == "vector_search":
                case_result = task["result"]
        if case_result != '':
            try:
                writer = get_stream_writer()
                writer(json.dumps({"AI_AGENT_FLOW": "✨ 分析完成，算算正在尝试生成违规案例人物关系穿透图..."},
                                  ensure_ascii=False))

                # 调用AI生成图表数据
                final_result = await self.character_relationships_generator.generate_response(case_result, messages)

                # 将AI生成结果格式化为AI_AGENT_CHART类型
                writer(json.dumps({
                    "AI_AGENT_CHARACTER": final_result
                }, ensure_ascii=False))
            except Exception as e:
                logger.warning(f"发送流程信息失败: {e}")
                final_result = str(e)

            return {
                "task_results": []
            }
        return None

    async def _final_merge_node(self, state: GenericWorkflowState) -> Dict[str, Any]:
        """最终汇聚节点 - 等待并行任务完成"""
        # 这个节点主要是等待两个并行任务都完成
        # 可以在这里做一些最终的处理，比如合并结果

        # 如果需要，可以将图表结果和文本结果进行最终整合
        messages = state.get("messages", [])

        return {
            "messages": messages,  # 保持现有的消息
            "task_results": "clear",
        }

    def compile(self, **kwargs):
        """编译工作流图，支持自定义配置（如 checkpointer）"""
        return self.graph.compile(**kwargs)

    def get_initial_state(self, message: str, **kwargs) -> Dict[str, Any]:
        """获取初始状态模板，用户可以基于此自定义"""
        return {
            "messages": [("user", message)],
            "context": kwargs.get("context", {}),
            "intent_type": None,
            "user_goal": None,
            "approach": None,
            "current_tasks": None,
            "task_results": [],
            "iteration_count": 0,
            "max_iterations": kwargs.get("max_iterations", 5),
            "need_more_tasks": False,
            "new_iteration_tasks": [],
            "final_result": None,
            # 支持用户添加自定义字段
            **{k: v for k, v in kwargs.items() if k not in ["context", "max_iterations"]}
        }

    async def run(self, message: str, context: Dict[str, Any] = None, **kwargs) -> str:
        """简单的运行方法（向后兼容）"""
        compiled_graph = self.compile()
        initial_state = self.get_initial_state(message, context=context, **kwargs)

        result = await compiled_graph.ainvoke(initial_state)
        return result["messages"][-1].content


# 便捷函数
def create_generic_workflow(llm=None):
    """创建通用工作流实例"""
    if llm is None:
        llm = get_a_llm(llm_type='hs-deepseek-v3-0324', temperature=0)

    return GenericWorkflowFramework(llm)


# 示例使用
async def example_usage():
    """示例用法"""
    # 创建工作流
    workflow = create_generic_workflow()

    # 运行示例
    result1 = await workflow.run("你好")
    print("简单对话示例:", result1)

    result2 = await workflow.run("查询用户数据")
    print("查询任务示例:", result2)

    result3 = await workflow.run("对比分析今年和去年的销售情况")
    print("迭代分析示例:", result3)


# 持久化使用示例
async def persistence_example():
    """展示如何使用持久化"""
    # 创建工作流
    workflow = create_generic_workflow()

    # 自定义初始状态
    initial_state = workflow.get_initial_state(
        message="查询用户数据",
        login_company="test_company",  # 自定义字段
        max_iterations=5  # 自定义最大迭代次数
    )

    # 自定义配置
    config = {
        "recursion_limit": 20,
        "configurable": {
            "thread_id": "112233445511223344"  # 持久化线程ID
        }
    }

    # 方式1：不使用持久化（简单编译）
    simple_graph = workflow.compile()
    result = await simple_graph.ainvoke(initial_state, config)
    print("无持久化结果:", result["messages"][-1].content)

    # 方式2：使用持久化（需要你的 checkpointer）
    # async with MyAIOMySQLSaver.from_conn_string(agent_checkpoint_db_uri) as checkpointer:
    #     persistent_graph = workflow.compile(checkpointer=checkpointer)
    #     result = await persistent_graph.ainvoke(initial_state, config, stream_mode="values", debug=True)
    #     print("持久化结果:", result["messages"][-1].content)


if __name__ == "__main__":
    import asyncio

    print("=== 通用多Agent工作流框架 ===")
    print("核心组件：监督者Agent → 快速规划器 → 任务分发器 → 工作节点 → 迭代检查 → 结果合并器")
    print("支持：意图识别、快速规划、并行执行、迭代调整、结果合并")
    print("模拟人类思维：理解问题 → 快速规划 → 并行执行 → 边做边想 → 整合回答")
    print()

    # 运行示例
    asyncio.run(example_usage())

    print("\n=== 持久化使用示例 ===")
    asyncio.run(persistence_example())
