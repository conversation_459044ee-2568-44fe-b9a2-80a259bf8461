"""
重构后的智能体工作流框架
基于 LangGraph 的真正 Agent 模式，使用工具调用和消息驱动
核心特点：
1. 使用 @tool 装饰器定义工具
2. 使用 ToolNode 自动处理工具调用
3. 简化状态管理，基于消息传递
4. Agent 之间通过消息进行动态交互
"""

import json
import operator
from typing import Annotated, Optional, Dict, Any, List, Literal

from langchain_core.messages import AnyMessage, AIMessage, HumanMessage, ToolMessage
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.tools import tool
from langgraph.config import get_stream_writer
from langgraph.graph import StateGraph, START, END, add_messages
from langgraph.prebuilt import ToolNode
from typing_extensions import TypedDict

from sub_agents.rep.utils.llm_utils import get_a_llm
from sub_agents.sql_database_chain.violation_data_search_new.core.vector_search import execute_multi_query_rerank_search
from utils.load_env import logger


# 简化的状态定义 - 基于消息驱动
class AgentState(TypedDict):
    """简化的智能体状态，主要基于消息传递"""
    messages: Annotated[list[AnyMessage], add_messages]
    context: Optional[Dict[str, Any]]  # 可选的业务上下文


# 工具定义 - 使用 @tool 装饰器
@tool
async def vector_database_search(query: str) -> dict:
    """
    在违规案例向量数据库中进行搜索。
    当你需要查询具体的违规案例、公司信息或相关法规时，使用此工具。
    
    Args:
        query: 搜索查询语句
        
    Returns:
        包含搜索结果的字典
    """
    try:
        logger.info(f"[VectorTool] 执行向量搜索: {query}")
        
        # 发送流程信息
        try:
            writer = get_stream_writer()
            writer(json.dumps({"AI_AGENT_FLOW": f"🔍 正在向量数据库中搜索: {query}"}, ensure_ascii=False))
        except Exception:
            pass
            
        result_dict = await execute_multi_query_rerank_search(query)
        
        # 推送向量检索数据
        try:
            writer = get_stream_writer()
            writer(json.dumps({'AI_AGENT_VECTOR': result_dict}, ensure_ascii=False))
        except Exception:
            pass
            
        return result_dict
        
    except Exception as e:
        logger.error(f"[VectorTool] 搜索失败: {e}")
        return {"error": f"搜索失败: {str(e)}"}


@tool
async def generate_chart(analysis_content: str) -> dict:
    """
    根据分析内容生成可视化图表。
    当分析结果需要图表展示时使用此工具。
    
    Args:
        analysis_content: 需要可视化的分析内容
        
    Returns:
        包含图表配置的字典
    """
    try:
        logger.info(f"[ChartTool] 生成图表: {analysis_content[:100]}...")
        
        # 发送流程信息
        try:
            writer = get_stream_writer()
            writer(json.dumps({"AI_AGENT_FLOW": "📈 正在生成可视化图表..."}, ensure_ascii=False))
        except Exception:
            pass
        
        # 这里可以调用图表生成逻辑
        # 暂时返回一个示例图表配置
        chart_config = {
            "id": "chart_001",
            "name": "违规案例分析图",
            "type": "bar",
            "option": {
                "title": {"text": "违规案例统计"},
                "tooltip": {},
                "xAxis": {"type": "category", "data": ["案例1", "案例2", "案例3"]},
                "yAxis": {"type": "value"},
                "series": [{"type": "bar", "data": [10, 20, 30]}]
            }
        }
        
        # 推送图表数据
        try:
            writer = get_stream_writer()
            writer(json.dumps({"AI_AGENT_CHART": chart_config}, ensure_ascii=False))
        except Exception:
            pass
            
        return chart_config
        
    except Exception as e:
        logger.error(f"[ChartTool] 图表生成失败: {e}")
        return {"error": f"图表生成失败: {str(e)}"}


# 工具列表
tools = [vector_database_search, generate_chart]
tool_node = ToolNode(tools)


# 监督者 Agent
class SupervisorAgent:
    """监督者智能体，负责意图识别和路由决策"""
    
    def __init__(self, llm):
        self.llm = llm
        self.prompt = ChatPromptTemplate.from_messages([
            ("system", """你是一个智能助手的监督者，负责识别用户意图并决定处理路径。

你需要判断用户输入属于以下哪种类型：

1. **simple_chat（简单对话）**：
   - 问候语：你好、hi、hello等
   - 感谢语：谢谢、感谢等
   - 闲聊：天气、心情、无关话题等
   - 测试性输入：测试、test等

2. **query_task（查询任务）**：
   - 任何需要查询、搜索、分析的请求
   - 数据查询、信息检索、内容搜索
   - 统计分析、对比分析等

请直接回复 "simple_chat" 或 "query_task"，不要添加其他内容。"""),
            ("placeholder", "{messages}"),
        ])

    async def classify_intent(self, state: AgentState) -> Dict[str, Any]:
        """分类用户意图"""
        try:
            messages = state.get("messages", [])
            chain = self.prompt | self.llm
            result = await chain.ainvoke({"messages": messages})
            
            intent_type = result.content.strip().lower()
            if intent_type not in ["simple_chat", "query_task"]:
                intent_type = "query_task"  # 默认为查询任务
                
            logger.info(f"[SupervisorAgent] 意图识别: {intent_type}")
            
            return {
                "messages": [AIMessage(content=f"意图类型: {intent_type}")],
                "intent_type": intent_type
            }
            
        except Exception as e:
            logger.warning(f"[SupervisorAgent] 意图识别失败: {e}")
            return {
                "messages": [AIMessage(content="意图类型: query_task")],
                "intent_type": "query_task"
            }


# 简单回复 Agent
class SimpleResponseAgent:
    """处理简单对话的智能体"""
    
    def __init__(self, llm):
        self.llm = llm
        self.prompt = ChatPromptTemplate.from_messages([
            ("system", """你是一个专业、友好的违规案例查询助手。用户刚才发送了简单的对话内容，请生成一个合适的回复。

你的特点：
- 专业但亲切，不过分正式
- 主动介绍你的能力（数据查询、分析等）
- 引导用户提出具体问题
- 回复简洁明了，不要太长

请直接返回回复内容，不要添加额外格式。"""),
            ("placeholder", "{messages}"),
        ])

    async def respond(self, state: AgentState) -> Dict[str, Any]:
        """生成简单回复"""
        try:
            messages = state.get("messages", [])
            chain = self.prompt | self.llm
            result = await chain.ainvoke({"messages": messages})
            
            return {"messages": [AIMessage(content=result.content)]}
            
        except Exception as e:
            logger.warning(f"[SimpleResponseAgent] 回复生成失败: {e}")
            return {"messages": [AIMessage(content="您好！我是智能助手，可以帮您查询和分析各种信息。请问有什么可以帮助您的吗？")]}


# 规划 Agent
class PlannerAgent:
    """规划智能体，负责分析问题并决定调用哪些工具"""
    
    def __init__(self, llm):
        self.llm = llm
        # 绑定工具到 LLM
        self.llm_with_tools = llm.bind_tools(tools)
        
        self.prompt = ChatPromptTemplate.from_messages([
            ("system", """你是一个专业的违规案例分析专家，负责理解用户需求并调用合适的工具来获取信息。

你的工作流程：
1. 分析用户的问题和需求
2. 决定需要调用哪些工具来获取信息
3. 如果已经有足够的工具执行结果，则基于这些结果生成最终回答
4. 如果需要更多信息，继续调用工具

可用工具：
- vector_database_search: 在违规案例数据库中搜索相关案例
- generate_chart: 根据分析结果生成可视化图表

重要原则：
- 如果用户问题需要查询具体案例，使用 vector_database_search
- 如果分析结果适合用图表展示，使用 generate_chart
- 如果已经有工具执行结果，基于结果生成专业的分析回答
- 保持回答的专业性和准确性"""),
            ("placeholder", "{messages}"),
        ])

    async def plan_and_execute(self, state: AgentState) -> Dict[str, Any]:
        """规划并执行或生成回答"""
        try:
            messages = state.get("messages", [])
            
            # 发送流程信息
            try:
                writer = get_stream_writer()
                writer(json.dumps({"AI_AGENT_FLOW": "🧠 正在分析您的需求..."}, ensure_ascii=False))
            except Exception:
                pass
            
            chain = self.prompt | self.llm_with_tools
            result = await chain.ainvoke({"messages": messages})
            
            return {"messages": [result]}
            
        except Exception as e:
            logger.error(f"[PlannerAgent] 规划失败: {e}")
            return {"messages": [AIMessage(content=f"抱歉，处理您的请求时出现了问题：{str(e)}")]}


# 智能体工作流框架
class AgenticWorkflowFramework:
    """基于真正 Agent 模式的工作流框架"""
    
    def __init__(self, llm=None):
        if llm is None:
            llm = get_a_llm(llm_type='hs-deepseek-v3-0324', temperature=0)
        
        self.llm = llm
        
        # 初始化智能体
        self.supervisor = SupervisorAgent(llm)
        self.simple_response_agent = SimpleResponseAgent(llm)
        self.planner = PlannerAgent(llm)
        
        # 构建工作流图
        self.graph = self._build_graph()

    def _build_graph(self) -> StateGraph:
        """构建智能体工作流图"""
        graph = StateGraph(AgentState)
        
        # 添加节点
        graph.add_node("supervisor", self._supervisor_node)
        graph.add_node("simple_response", self._simple_response_node)
        graph.add_node("planner", self._planner_node)
        graph.add_node("tools", tool_node)
        
        # 设置流程
        graph.add_edge(START, "supervisor")
        
        # 监督者路由
        graph.add_conditional_edges(
            "supervisor",
            self._supervisor_router,
            {
                "simple_response": "simple_response",
                "planner": "planner"
            }
        )
        
        # 简单回复直接结束
        graph.add_edge("simple_response", END)
        
        # 规划器的条件路由
        graph.add_conditional_edges(
            "planner",
            self._should_continue,
            {
                "tools": "tools",
                "end": END
            }
        )
        
        # 工具执行后返回规划器
        graph.add_edge("tools", "planner")
        
        return graph
