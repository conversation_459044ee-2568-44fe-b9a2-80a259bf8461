"""
从工作流模式到智能体模式的迁移指南
逐步重构现有代码的详细步骤
"""

# ============================================================================
# 第一步：将执行器转换为工具
# ============================================================================

# 原始代码：GenericQueryExecutor
class OriginalExecutor:
    async def execute(self, task: Dict[str, Any], context: Dict[str, Any] = None) -> str:
        query = task.get("query", "")
        result_dict = await execute_multi_query_rerank_search(query)
        # 复杂的结果处理逻辑...
        return result

# 重构为工具
from langchain_core.tools import tool

@tool
async def violation_search_tool(query: str) -> dict:
    """搜索违规案例数据库"""
    result_dict = await execute_multi_query_rerank_search(query)
    return result_dict

# 迁移步骤：
"""
1. 将 GenericQueryExecutor.execute() 方法转换为 @tool 函数
2. 简化参数：只保留核心的 query 参数
3. 简化返回值：直接返回原始结果，让 LLM 处理格式化
4. 添加清晰的工具描述，帮助 LLM 理解何时使用
"""


# ============================================================================
# 第二步：重构 Agent 类
# ============================================================================

# 原始代码：HumanLikePlanner
class OriginalPlanner:
    async def plan(self, message: AnyMessage) -> Dict[str, Any]:
        # 返回复杂的字典结构
        return {
            "user_goal": "...",
            "approach": "...", 
            "initial_tasks": [...],
            "need_chart": True
        }

# 重构为真正的 Agent
class RefactoredPlannerAgent:
    def __init__(self, llm):
        self.llm_with_tools = llm.bind_tools(tools)  # 绑定工具
        self.prompt = ChatPromptTemplate.from_messages([
            ("system", """你是专业分析师。
            可用工具：violation_search_tool, generate_chart_tool
            
            工作流程：
            1. 分析用户问题
            2. 调用工具获取信息  
            3. 基于结果决定是否需要更多工具
            4. 提供最终分析"""),
            ("placeholder", "{messages}"),
        ])
    
    async def process(self, messages: List[AnyMessage]) -> AIMessage:
        # 返回 AIMessage，可能包含工具调用
        response = await (self.prompt | self.llm_with_tools).ainvoke({"messages": messages})
        return response

# 迁移步骤：
"""
1. 将 plan() 方法改为 process() 方法
2. 输入改为 messages 列表而不是单个 message
3. 输出改为 AIMessage 而不是字典
4. 使用 llm.bind_tools() 让 LLM 自动决定工具调用
5. 将原来的规划逻辑写入 system prompt
"""


# ============================================================================
# 第三步：简化状态管理
# ============================================================================

# 原始状态：复杂的全局状态
class OriginalState(TypedDict):
    messages: list
    context: dict
    intent_type: str
    user_goal: str
    approach: str
    need_chart: bool
    current_tasks: list
    task_results: list
    iteration_count: int
    # ... 更多字段

# 重构状态：简化的消息驱动
class RefactoredState(TypedDict):
    messages: Annotated[list[AnyMessage], add_messages]
    context: Optional[Dict[str, Any]]

# 迁移步骤：
"""
1. 删除所有中间状态字段
2. 只保留 messages 和 context
3. 所有信息通过消息传递：
   - 用户输入 -> HumanMessage
   - Agent 思考 -> AIMessage  
   - 工具调用 -> AIMessage with tool_calls
   - 工具结果 -> ToolMessage
   - 最终回答 -> AIMessage
"""


# ============================================================================
# 第四步：重构图结构
# ============================================================================

# 原始图：固定的工作流
def original_graph():
    graph.add_edge(START, "supervisor")
    graph.add_edge("supervisor", "planning") 
    graph.add_edge("planning", "dispatcher")
    graph.add_edge("dispatcher", "task_worker")
    graph.add_edge("task_worker", "iteration_check")
    graph.add_edge("iteration_check", "aggregation")
    graph.add_edge("aggregation", END)

# 重构图：动态的智能体交互
def refactored_graph():
    graph.add_edge(START, "supervisor")
    
    # 监督者动态路由
    graph.add_conditional_edges(
        "supervisor",
        supervisor_router,
        {"analyst": "analyst", "end": END}
    )
    
    # 分析师动态路由
    graph.add_conditional_edges(
        "analyst", 
        should_continue,
        {"tools": "tools", "end": END}
    )
    
    # 工具执行后回到分析师
    graph.add_edge("tools", "analyst")

# 迁移步骤：
"""
1. 删除固定的节点序列
2. 建立 Agent -> Tools -> Agent 的核心循环
3. 使用条件边进行动态路由
4. 让 Agent 自主决定何时结束
"""


# ============================================================================
# 第五步：迁移工具注册逻辑
# ============================================================================

# 原始代码：手动工具注册
class OriginalToolRegistry:
    def __init__(self):
        self.tool_registry = ToolRegistry()
        self.tool_registry.register("query_tool", GenericQueryExecutor())
    
    async def _task_worker_node(self, state):
        task = state.get("task", {})
        tool_type = task.get('tool_type', 'query_tool')
        executor = self.tool_registry.get(tool_type)
        result = await executor.execute(task, context)
        return {"task_results": [{"result": result}]}

# 重构代码：自动工具管理
from langgraph.prebuilt import ToolNode

tools = [violation_search_tool, generate_chart_tool]
tool_node = ToolNode(tools)

class RefactoredWorkflow:
    def __init__(self, llm):
        self.analyst = AnalystAgent(llm)  # LLM 已绑定工具
        
    def _build_graph(self):
        graph.add_node("analyst", self._analyst_node)
        graph.add_node("tools", tool_node)  # 自动工具节点

# 迁移步骤：
"""
1. 删除 ToolRegistry 相关代码
2. 将执行器转换为 @tool 函数
3. 使用 ToolNode 自动管理工具执行
4. 让 LLM 通过 bind_tools 自动调用工具
"""


# ============================================================================
# 第六步：迁移迭代逻辑
# ============================================================================

# 原始代码：外部迭代控制
class OriginalIteration:
    async def _iteration_check_node(self, state):
        decision = await self.iteration_decider.decide(...)
        if decision.need_more_tasks:
            return {"need_more_tasks": True, "new_tasks": [...]}
        return {"need_more_tasks": False}
    
    def _iteration_router(self, state):
        if state.get("need_more_tasks"):
            return "dispatcher"
        return "aggregation"

# 重构代码：Agent 内化迭代
class RefactoredAgent:
    def __init__(self, llm):
        self.llm_with_tools = llm.bind_tools(tools)
        self.prompt = ChatPromptTemplate.from_messages([
            ("system", """你是分析师。工作流程：
            1. 分析问题
            2. 调用工具获取信息
            3. 检查结果是否充分
            4. 如果不够，调用更多工具
            5. 如果充分，提供最终答案"""),
            ("placeholder", "{messages}"),
        ])
    
    async def process(self, messages):
        # Agent 自主决定是否需要更多信息
        response = await (self.prompt | self.llm_with_tools).ainvoke({"messages": messages})
        return response

# 迁移步骤：
"""
1. 删除 IterationDecider 类
2. 删除专门的迭代检查节点
3. 将迭代逻辑写入 Agent 的 system prompt
4. 让 Agent 自主决定是否需要更多工具调用
"""


# ============================================================================
# 完整的迁移检查清单
# ============================================================================

class MigrationChecklist:
    """迁移检查清单"""
    
    def checklist(self):
        return """
        ✅ 第一步：工具转换
        [ ] 将 GenericQueryExecutor 转换为 @tool 函数
        [ ] 将 ChartGenerator 转换为 @tool 函数
        [ ] 删除 ToolRegistry 相关代码
        [ ] 创建 ToolNode 实例
        
        ✅ 第二步：Agent 重构
        [ ] 将 HumanLikePlanner 改为返回 AIMessage
        [ ] 使用 llm.bind_tools() 绑定工具
        [ ] 将规划逻辑写入 system prompt
        [ ] 删除 PydanticOutputParser
        
        ✅ 第三步：状态简化
        [ ] 删除复杂的状态字段
        [ ] 只保留 messages 和 context
        [ ] 确保所有信息通过消息传递
        
        ✅ 第四步：图重构
        [ ] 删除固定的节点序列
        [ ] 建立 Agent -> Tools -> Agent 循环
        [ ] 实现动态路由函数
        [ ] 删除不必要的中间节点
        
        ✅ 第五步：迭代重构
        [ ] 删除 IterationDecider
        [ ] 删除迭代检查节点
        [ ] 将迭代逻辑内化到 Agent prompt
        
        ✅ 第六步：测试验证
        [ ] 测试简单对话场景
        [ ] 测试单次工具调用场景
        [ ] 测试多次工具调用场景
        [ ] 测试错误处理场景
        """

# ============================================================================
# 迁移后的优势
# ============================================================================

class MigrationBenefits:
    """迁移后的优势"""
    
    def benefits(self):
        return """
        🎯 代码简化：
        - 状态管理从 15+ 字段减少到 2 个字段
        - 图节点从 8+ 个减少到 3 个核心节点
        - 删除了大量的中间处理逻辑
        
        🚀 扩展性提升：
        - 添加新工具只需要一个 @tool 函数
        - Agent 自动学会使用新工具
        - 无需修改图结构或状态定义
        
        🧠 智能化增强：
        - Agent 可以自主决策工具调用
        - 支持动态的多轮工具调用
        - 更自然的对话交互
        
        🔧 维护性改善：
        - 更清晰的职责分离
        - 更少的耦合依赖
        - 更容易调试和测试
        
        📈 性能优化：
        - 减少了不必要的状态传递
        - 更高效的消息处理
        - 更好的并发支持
        """


if __name__ == "__main__":
    print("=== 迁移指南 ===")
    checklist = MigrationChecklist()
    print(checklist.checklist())
    
    benefits = MigrationBenefits()
    print(benefits.benefits())
