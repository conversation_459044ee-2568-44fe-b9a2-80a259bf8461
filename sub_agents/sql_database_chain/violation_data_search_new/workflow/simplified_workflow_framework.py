"""
简化的智能工作流框架 - 解决过度工程化问题

核心改进：
1. 去除冗余的工作级规划层，使用单一的智能任务规划器
2. 使用枚举类型替代魔术字符串
3. 分离不可变状态和可变状态
4. 增加任务失败处理、重试机制、并发控制
5. 改进降级逻辑和错误处理
"""
import json
import operator
import asyncio
from enum import Enum
from typing import Annotated, Optional, Dict, Any, List, Union
from dataclasses import dataclass, field

from langchain_core.messages import AnyMessage, AIMessage
from langchain_core.output_parsers import PydanticOutputParser
from langchain_core.prompts import ChatPromptTemplate
from langgraph.config import get_stream_writer
from langgraph.graph import StateGraph, START, END, add_messages
from langgraph.types import Send
from pydantic import BaseModel, Field
from typing_extensions import Literal, TypedDict

from sub_agents.rep.utils.llm_utils import get_a_llm
from sub_agents.sql_database_chain.violation_data_search_new.core.vector_search import execute_multi_query_rerank_search
from utils.load_env import logger


# 使用枚举替代魔术字符串
class TaskStatus(Enum):
    NOT_STARTED = "not_started"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    RETRYING = "retrying"


class IntentType(Enum):
    SIMPLE_CHAT = "simple_chat"
    TASK_REQUEST = "task_request"


# 不可变的规划状态
@dataclass(frozen=True)
class PlanningState:
    """不可变的规划结果状态"""
    user_goal: str
    approach: str
    need_chart: bool
    max_iterations: int = 5


# 可变的执行状态
class ExecutionState(TypedDict):
    """可变的执行状态"""
    messages: Annotated[list[AnyMessage], add_messages]
    context: Optional[Dict[str, Any]]
    
    # 规划结果（一次性设置，后续只读）
    planning: Optional[PlanningState]
    intent_type: Optional[IntentType]
    
    # 任务执行状态（可变）
    pending_tasks: List['Task']
    completed_tasks: Annotated[List['TaskResult'], operator.add]
    failed_tasks: List['TaskResult']
    
    # 执行控制
    iteration_count: int
    max_concurrent_tasks: int


class Task(BaseModel):
    """任务模型 - 使用枚举类型"""
    id: str = Field(description="任务ID")
    description: str = Field(description="任务描述")
    query: str = Field(description="查询内容")
    tool_type: str = Field(description="工具类型")
    intent: str = Field(description="任务意图")
    status: TaskStatus = Field(default=TaskStatus.NOT_STARTED, description="任务状态")
    dependencies: List[str] = Field(default=[], description="依赖的任务ID列表")
    priority: int = Field(default=1, description="任务优先级")
    retry_count: int = Field(default=0, description="重试次数")
    max_retries: int = Field(default=2, description="最大重试次数")


class TaskResult(BaseModel):
    """任务执行结果"""
    task_id: str = Field(description="任务ID")
    description: str = Field(description="任务描述")
    tool_type: str = Field(description="使用的工具类型")
    result: str = Field(description="执行结果")
    status: TaskStatus = Field(description="执行状态")
    execution_time: Optional[float] = Field(default=None, description="执行耗时（秒）")
    error_message: Optional[str] = Field(default=None, description="错误信息")


class PlanningResult(BaseModel):
    """智能任务规划结果"""
    reasoning: str = Field(description="规划思考过程")
    user_goal: str = Field(description="用户的真实目标")
    approach: str = Field(description="解决思路")
    initial_tasks: List[Task] = Field(description="初始任务列表")
    need_chart: bool = Field(description="是否需要生成图表")


class IntentClassificationResult(BaseModel):
    """意图分类结果"""
    intent_type: IntentType = Field(description="意图类型")
    confidence: float = Field(description="置信度")


# 简单对话回复生成器
class SimpleResponseGenerator:
    """处理简单对话的回复生成器"""

    def __init__(self, llm):
        self.llm = llm
        self.prompt = ChatPromptTemplate.from_messages([
            ("system", """你是一个专业、友好的智能助手。用户发送了简单的对话内容，请生成合适的回复。

你的特点：
- 专业但亲切，不过分正式
- 主动介绍你的能力（数据查询、分析等）
- 引导用户提出具体问题
- 回复简洁明了

请直接返回回复内容，不要添加额外格式。"""),
            ("user", "{user_input}")
        ])

    async def generate_response(self, user_input: str) -> str:
        """生成简单对话回复"""
        try:
            chain = self.prompt | self.llm
            response = await chain.ainvoke({"user_input": user_input})
            return response.content
        except Exception as e:
            logger.warning(f"简单回复生成失败: {e}")
            return "您好！我是智能助手，可以帮您查询和分析各种信息。请问有什么可以帮助您的吗？"


# 意图理解层
class IntentClassifier:
    """意图理解与分类层 - 简化版本"""

    def __init__(self, llm):
        self.llm = llm
        self.parser = PydanticOutputParser(pydantic_object=IntentClassificationResult)
        
        self.prompt = ChatPromptTemplate.from_messages([
            ("system", """你是智能助手的意图分类器。

判断用户输入类型：
1. **simple_chat**: 问候、感谢、闲聊等简单对话
2. **task_request**: 需要查询、分析、处理的任务请求

判断标准：
- simple_chat: 问候语、感谢语、测试输入、闲聊内容
- task_request: 包含查询需求、分析要求、具体问题

{format_instructions}

示例：
用户："你好" -> {{"intent_type": "simple_chat", "confidence": 0.95}}
用户："查询用户数据" -> {{"intent_type": "task_request", "confidence": 0.98}}"""),
            ("placeholder", "{messages}"),
        ])

    async def classify(self, messages: List[AnyMessage]) -> IntentClassificationResult:
        """分类用户意图"""
        try:
            chain = self.prompt | self.llm | self.parser
            result = await chain.ainvoke({
                "messages": messages,
                "format_instructions": self.parser.get_format_instructions()
            })
            logger.info(f"[IntentClassifier] 意图分类: {result.intent_type}")
            return result
        except Exception as e:
            logger.warning(f"意图分类失败，默认为任务请求: {e}")
            return IntentClassificationResult(
                intent_type=IntentType.TASK_REQUEST,
                confidence=0.5
            )


# 智能任务规划器 - 一步到位
class MasterTaskPlanner:
    """智能任务规划器 - 一步到位的任务分解"""

    def __init__(self, llm, tool_registry):
        self.llm = llm
        self.tool_registry = tool_registry
        self.parser = PydanticOutputParser(pydantic_object=PlanningResult)

    async def plan(self, message: AnyMessage) -> PlanningResult:
        """一步到位的智能任务规划"""
        available_tools = self.tool_registry.get_available_tools_desc()
        
        prompt = ChatPromptTemplate.from_messages([
            ("system", """你是智能任务规划师，负责将用户需求直接转化为可执行的任务列表。

# 工作流程
## 先思考 (Reasoning)
- 深度分析用户需求，识别核心目标
- 确定解决思路和所需信息
- 设计具体的执行步骤
- 建议控制思考过程在 200 字以内

## 然后规划 (Planning)
可用工具：
{available_tools}

# 任务拆解规则
- **数量限制**：最多5个任务，避免过度拆解
- **独立性**：任务之间不重复、不交叠
- **可执行性**：每个任务都能通过现有工具完成
- **依赖关系**：合理设置任务间的依赖关系

# 多维度查询策略
- 复杂查询需要多个维度的搜索
- 每个查询词包含多个关键词
- 时间信息需要具体化（如"最近三年"改为"2021-2024年"）

{format_instructions}"""),
            ("user", "{user_input}")
        ])

        try:
            chain = prompt | self.llm | self.parser
            result = await chain.ainvoke({
                "user_input": str(message.content),
                "available_tools": available_tools,
                "format_instructions": self.parser.get_format_instructions()
            })
            logger.info(f"[MasterTaskPlanner] 任务规划完成，生成{len(result.initial_tasks)}个任务")
            return result
        except Exception as e:
            logger.warning(f"任务规划失败，使用默认规划: {e}")
            return self._get_default_plan(message)

    def _get_default_plan(self, message: AnyMessage) -> PlanningResult:
        """默认任务规划"""
        return PlanningResult(
            reasoning="由于规划失败，采用简单直接的处理方式",
            user_goal=str(message.content),
            approach="直接查询获取信息",
            initial_tasks=[Task(
                id="task_1",
                description=str(message.content),
                query=str(message.content),
                tool_type="query_tool",
                intent="获取信息"
            )],
            need_chart=False
        )


# 健壮的任务执行器
class RobustTaskExecutor:
    """健壮的任务执行器 - 支持重试和错误处理"""

    def __init__(self, tool_registry):
        self.tool_registry = tool_registry

    async def execute_task(self, task: Task, context: Dict[str, Any] = None) -> TaskResult:
        """执行单个任务，支持重试"""
        import time

        start_time = time.time()

        for attempt in range(task.max_retries + 1):
            try:
                # 更新任务状态
                if attempt > 0:
                    task.status = TaskStatus.RETRYING
                    task.retry_count = attempt
                    logger.info(f"[TaskExecutor] 重试任务 {task.id}，第{attempt}次")
                else:
                    task.status = TaskStatus.IN_PROGRESS

                # 获取工具执行器
                executor = self.tool_registry.get(task.tool_type)

                # 执行任务
                task_dict = task.model_dump()
                result = await executor.execute(task_dict, context or {})

                execution_time = time.time() - start_time

                # 成功执行
                return TaskResult(
                    task_id=task.id,
                    description=task.description,
                    tool_type=task.tool_type,
                    result=result,
                    status=TaskStatus.COMPLETED,
                    execution_time=execution_time
                )

            except Exception as e:
                error_msg = f"执行失败: {str(e)}"
                logger.warning(f"[TaskExecutor] 任务 {task.id} 执行失败 (尝试 {attempt + 1}/{task.max_retries + 1}): {e}")

                # 如果是最后一次尝试，返回失败结果
                if attempt >= task.max_retries:
                    execution_time = time.time() - start_time
                    return TaskResult(
                        task_id=task.id,
                        description=task.description,
                        tool_type=task.tool_type,
                        result=error_msg,
                        status=TaskStatus.FAILED,
                        execution_time=execution_time,
                        error_message=str(e)
                    )

                # 等待后重试
                await asyncio.sleep(1 * (attempt + 1))  # 递增等待时间


# 并发控制的任务调度器
class ConcurrentTaskScheduler:
    """支持并发控制的任务调度器"""

    def __init__(self, executor: RobustTaskExecutor, max_concurrent: int = 3):
        self.executor = executor
        self.max_concurrent = max_concurrent
        self.semaphore = asyncio.Semaphore(max_concurrent)

    async def execute_ready_tasks(self, pending_tasks: List[Task], completed_task_ids: set,
                                context: Dict[str, Any] = None) -> List[TaskResult]:
        """并发执行就绪的任务"""
        # 找出依赖已满足的任务
        ready_tasks = []
        for task in pending_tasks:
            if all(dep_id in completed_task_ids for dep_id in task.dependencies):
                ready_tasks.append(task)

        if not ready_tasks:
            return []

        logger.info(f"[TaskScheduler] 并发执行 {len(ready_tasks)} 个任务")

        # 并发执行任务
        async def execute_with_semaphore(task: Task):
            async with self.semaphore:
                return await self.executor.execute_task(task, context)

        tasks = [execute_with_semaphore(task) for task in ready_tasks]
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # 处理结果
        task_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                # 处理异常情况
                task = ready_tasks[i]
                task_results.append(TaskResult(
                    task_id=task.id,
                    description=task.description,
                    tool_type=task.tool_type,
                    result=f"执行异常: {str(result)}",
                    status=TaskStatus.FAILED,
                    error_message=str(result)
                ))
            else:
                task_results.append(result)

        return task_results


# 智能迭代决策器
class SmartIterationDecider:
    """智能迭代决策器 - 支持任务修改、删除、插入"""

    def __init__(self, llm):
        self.llm = llm

    async def decide_next_action(self, user_goal: str, approach: str,
                               completed_tasks: List[TaskResult],
                               failed_tasks: List[TaskResult],
                               messages: List[AnyMessage]) -> Dict[str, Any]:
        """决定下一步行动"""
        # 简化版本：只支持添加新任务
        # 未来可以扩展支持修改、删除、插入

        if len(completed_tasks) >= 5:  # 限制最大任务数
            return {"action": "stop", "reason": "已达到最大任务数"}

        if len(failed_tasks) > len(completed_tasks):  # 失败太多
            return {"action": "stop", "reason": "失败任务过多"}

        # 简单的启发式规则
        if len(completed_tasks) < 2 and "分析" in user_goal:
            return {
                "action": "add_tasks",
                "new_tasks": [Task(
                    id=f"task_{len(completed_tasks) + 1}",
                    description="补充分析数据",
                    query=f"{user_goal} 补充信息",
                    tool_type="query_tool",
                    intent="获取更多信息"
                )]
            }

        return {"action": "stop", "reason": "信息已足够"}


# 工具注册表
class ToolRegistry:
    """工具注册表"""

    def __init__(self):
        self._tools: Dict[str, Any] = {}
        self._tool_descriptions: Dict[str, str] = {}

    def register(self, tool_type: str, executor: Any, description: str = ""):
        """注册工具执行器"""
        self._tools[tool_type] = executor
        self._tool_descriptions[tool_type] = description

    def get(self, tool_type: str) -> Any:
        """获取工具执行器"""
        if tool_type not in self._tools:
            raise ValueError(f"未注册的工具类型: {tool_type}")
        return self._tools[tool_type]

    def get_available_tools_desc(self) -> str:
        """获取可用工具描述"""
        if not self._tool_descriptions:
            return "- query_tool: 通用查询工具（默认）"

        descriptions = []
        for tool_type, desc in self._tool_descriptions.items():
            descriptions.append(f"- {tool_type}: {desc}")
        return "\n".join(descriptions)


# 通用查询执行器
class GenericQueryExecutor:
    """通用查询执行器"""

    async def execute(self, task: Dict[str, Any], context: Dict[str, Any] = None) -> str:
        """执行查询任务"""
        query = task.get("query", "")

        # 调用向量库查询
        result_dict = await execute_multi_query_rerank_search(query)

        # 格式化结果
        if isinstance(result_dict, dict):
            search_summary = result_dict.get("search_summary", "")
            formatted_results = result_dict.get("formatted_results", [])

            result_strings = []
            for case in formatted_results:
                if isinstance(case, dict):
                    case_str = f"""案例{case.get('case_number', 'N/A')}：
- 案例标题: {case.get('title', 'N/A')}
- 公司名称: {case.get('company_name', 'N/A')} ({case.get('company_code', 'N/A')})
- 处罚机构: {case.get('punish_org', 'N/A')}
- 违规事项: {case.get('case_list', 'N/A')}
- 事件时间: {case.get('event_time', 'N/A')}"""
                    if case.get('content_preview'):
                        case_str += f"\n- 相关内容: {case.get('content_preview')}"
                    result_strings.append(case_str)
                else:
                    result_strings.append(str(case))

            return search_summary + "\n\n" + "\n\n".join(result_strings)
        else:
            return str(result_dict)


# 结果合并器
class ResultMerger:
    """结果合并器"""

    def __init__(self, llm):
        self.llm = llm
        self.prompt = ChatPromptTemplate.from_messages([
            ("system", """你是专业的智能助手，基于查询结果为用户提供准确、清晰的回答。

你的任务是基于查询结果，直接回答用户的问题。注意：
1. 用专业但友好的语言回答
2. 直接回应用户关心的核心问题
3. 结构清晰，先总结后详述
4. 避免重复信息，突出最有价值的内容

## 查询结果：
{results}"""),
            ("placeholder", "{messages}")
        ])

    async def merge(self, task_results: List[TaskResult], messages: List[AnyMessage]) -> str:
        """合并任务结果"""
        try:
            # 整理结果
            results_text = ""
            for i, task_result in enumerate(task_results, 1):
                if task_result.status == TaskStatus.COMPLETED:
                    results_text += f"{task_result.description}：\n{task_result.result}\n\n"
                else:
                    results_text += f"{task_result.description}：执行失败 - {task_result.error_message}\n\n"

            chain = self.prompt | self.llm
            response = await chain.ainvoke({
                "messages": messages,
                "results": results_text.strip()
            })
            return response.content

        except Exception as e:
            logger.warning(f"结果合并失败: {e}")
            # 降级处理：简单拼接结果
            if len(task_results) == 1:
                return f"根据查询结果：\n{task_results[0].result}"
            else:
                results = []
                for i, r in enumerate(task_results, 1):
                    results.append(f"结果{i}：{r.result}")
                return "\n\n".join(results)


# 简化的智能工作流框架
class SimplifiedWorkflowFramework:
    """简化的智能工作流框架 - 解决过度工程化问题"""

    def __init__(self, llm, max_concurrent_tasks: int = 3):
        self.llm = llm
        self.max_concurrent_tasks = max_concurrent_tasks

        # 初始化组件
        self.tool_registry = ToolRegistry()
        self.tool_registry.register("query_tool", GenericQueryExecutor(), "通用查询工具，支持向量搜索")

        self.intent_classifier = IntentClassifier(llm)
        self.simple_response_generator = SimpleResponseGenerator(llm)
        self.master_planner = MasterTaskPlanner(llm, self.tool_registry)
        self.task_executor = RobustTaskExecutor(self.tool_registry)
        self.task_scheduler = ConcurrentTaskScheduler(self.task_executor, max_concurrent_tasks)
        self.iteration_decider = SmartIterationDecider(llm)
        self.result_merger = ResultMerger(llm)

        # 构建工作流图
        self.graph = self._build_graph()

    def _build_graph(self) -> StateGraph:
        """构建简化的工作流图"""
        graph = StateGraph(ExecutionState)

        # 添加节点
        graph.add_node("classify_intent", self._classify_intent_node)
        graph.add_node("simple_response", self._simple_response_node)
        graph.add_node("plan_tasks", self._plan_tasks_node)
        graph.add_node("execute_tasks", self._execute_tasks_node)
        graph.add_node("check_iteration", self._check_iteration_node)
        graph.add_node("merge_results", self._merge_results_node)

        # 设置流程
        graph.add_edge(START, "classify_intent")

        # 意图分类路由
        def intent_router(state: ExecutionState) -> Literal["simple_response", "plan_tasks"]:
            return "simple_response" if state.get("intent_type") == IntentType.SIMPLE_CHAT else "plan_tasks"

        graph.add_conditional_edges(
            "classify_intent",
            intent_router,
            {
                "simple_response": "simple_response",
                "plan_tasks": "plan_tasks"
            }
        )

        graph.add_edge("simple_response", END)
        graph.add_edge("plan_tasks", "execute_tasks")
        graph.add_edge("execute_tasks", "check_iteration")

        # 迭代检查路由
        def iteration_router(state: ExecutionState) -> Literal["execute_tasks", "merge_results"]:
            return "execute_tasks" if state.get("pending_tasks") else "merge_results"

        graph.add_conditional_edges(
            "check_iteration",
            iteration_router,
            {
                "execute_tasks": "execute_tasks",
                "merge_results": "merge_results"
            }
        )

        graph.add_edge("merge_results", END)

        return graph

    async def _classify_intent_node(self, state: ExecutionState) -> Dict[str, Any]:
        """意图分类节点"""
        try:
            writer = get_stream_writer()
            writer(json.dumps({"AI_AGENT_FLOW": "🤔 正在理解您的需求..."}, ensure_ascii=False))
        except Exception:
            pass

        messages = state.get("messages", [])
        result = await self.intent_classifier.classify(messages)

        return {
            "intent_type": result.intent_type,
            "completed_tasks": [],
            "failed_tasks": [],
            "iteration_count": 0,
            "max_concurrent_tasks": self.max_concurrent_tasks
        }

    async def _simple_response_node(self, state: ExecutionState) -> Dict[str, Any]:
        """简单回复节点"""
        messages = state.get("messages", [])
        user_input = str(messages[-1].content) if messages else ""

        response = await self.simple_response_generator.generate_response(user_input)
        return {"messages": [AIMessage(content=response)]}

    async def _plan_tasks_node(self, state: ExecutionState) -> Dict[str, Any]:
        """任务规划节点"""
        try:
            writer = get_stream_writer()
            writer(json.dumps({"AI_AGENT_FLOW": "🧠 正在进行智能任务规划..."}, ensure_ascii=False))
        except Exception:
            pass

        messages = state.get("messages", [])
        if not messages:
            return {"pending_tasks": []}

        last_message = messages[-1]
        result = await self.master_planner.plan(last_message)

        # 创建不可变的规划状态
        planning_state = PlanningState(
            user_goal=result.user_goal,
            approach=result.approach,
            need_chart=result.need_chart
        )

        try:
            writer = get_stream_writer()
            task_count = len(result.initial_tasks)
            writer(json.dumps({"AI_AGENT_FLOW": f"🚀 启动{task_count}个智能搜索引擎..."}, ensure_ascii=False))
        except Exception:
            pass

        return {
            "planning": planning_state,
            "pending_tasks": result.initial_tasks
        }

    async def _execute_tasks_node(self, state: ExecutionState) -> Dict[str, Any]:
        """任务执行节点"""
        pending_tasks = state.get("pending_tasks", [])
        completed_tasks = state.get("completed_tasks", [])
        failed_tasks = state.get("failed_tasks", [])
        context = state.get("context", {})

        if not pending_tasks:
            return {}

        # 获取已完成任务的ID
        completed_task_ids = {task.task_id for task in completed_tasks}

        # 并发执行就绪的任务
        task_results = await self.task_scheduler.execute_ready_tasks(
            pending_tasks, completed_task_ids, context
        )

        if not task_results:
            return {}

        # 分离成功和失败的任务
        new_completed = []
        new_failed = []
        executed_task_ids = set()

        for result in task_results:
            executed_task_ids.add(result.task_id)
            if result.status == TaskStatus.COMPLETED:
                new_completed.append(result)
            else:
                new_failed.append(result)

        # 更新待执行任务列表
        remaining_tasks = [task for task in pending_tasks if task.id not in executed_task_ids]

        return {
            "pending_tasks": remaining_tasks,
            "completed_tasks": new_completed,
            "failed_tasks": new_failed
        }

    async def _check_iteration_node(self, state: ExecutionState) -> Dict[str, Any]:
        """迭代检查节点"""
        pending_tasks = state.get("pending_tasks", [])
        completed_tasks = state.get("completed_tasks", [])
        failed_tasks = state.get("failed_tasks", [])
        iteration_count = state.get("iteration_count", 0)
        planning = state.get("planning")

        # 如果还有待执行任务，继续执行
        if pending_tasks:
            return {"iteration_count": iteration_count + 1}

        # 检查是否需要更多任务
        if planning and iteration_count < planning.max_iterations:
            try:
                writer = get_stream_writer()
                writer(json.dumps({"AI_AGENT_FLOW": "📊 正在分析搜索结果..."}, ensure_ascii=False))
            except Exception:
                pass

            messages = state.get("messages", [])
            decision = await self.iteration_decider.decide_next_action(
                planning.user_goal, planning.approach, completed_tasks, failed_tasks, messages
            )

            if decision.get("action") == "add_tasks":
                new_tasks = decision.get("new_tasks", [])
                try:
                    writer = get_stream_writer()
                    writer(json.dumps({"AI_AGENT_FLOW": f"🔄 启动{len(new_tasks)}个补充搜索..."}, ensure_ascii=False))
                except Exception:
                    pass
                return {
                    "pending_tasks": new_tasks,
                    "iteration_count": iteration_count + 1
                }

        # 完成迭代
        try:
            writer = get_stream_writer()
            writer(json.dumps({"AI_AGENT_FLOW": "📝 搜索完成，正在整理报告..."}, ensure_ascii=False))
        except Exception:
            pass

        return {}

    async def _merge_results_node(self, state: ExecutionState) -> Dict[str, Any]:
        """结果合并节点"""
        completed_tasks = state.get("completed_tasks", [])
        messages = state.get("messages", [])

        if not completed_tasks:
            error_msg = "抱歉，没有获取到任何查询结果"
            return {"messages": [AIMessage(content=error_msg)]}

        final_result = await self.result_merger.merge(completed_tasks, messages)
        return {"messages": [AIMessage(content=final_result)]}

    def compile(self, **kwargs):
        """编译工作流图"""
        return self.graph.compile(**kwargs)

    def get_initial_state(self, message: str, **kwargs) -> Dict[str, Any]:
        """获取初始状态"""
        return {
            "messages": [("user", message)],
            "context": kwargs.get("context", {}),
            "planning": None,
            "intent_type": None,
            "pending_tasks": [],
            "completed_tasks": [],
            "failed_tasks": [],
            "iteration_count": 0,
            "max_concurrent_tasks": kwargs.get("max_concurrent_tasks", self.max_concurrent_tasks),
            **{k: v for k, v in kwargs.items() if k not in ["context", "max_concurrent_tasks"]}
        }

    async def run(self, message: str, context: Dict[str, Any] = None, **kwargs) -> str:
        """简单的运行方法"""
        compiled_graph = self.compile()
        initial_state = self.get_initial_state(message, context=context, **kwargs)

        result = await compiled_graph.ainvoke(initial_state,debug=True)
        return result["messages"][-1].content


# 便捷函数
def create_simplified_workflow(llm=None, max_concurrent_tasks: int = 3):
    """创建简化版工作流实例"""
    if llm is None:
        llm = get_a_llm(llm_type='hs-deepseek-v3-0324', temperature=0)

    return SimplifiedWorkflowFramework(llm, max_concurrent_tasks)


# 示例使用
async def example_usage():
    """示例用法"""
    workflow = create_simplified_workflow()

    print("=== 简化版工作流示例 ===")

    # 简单对话
    # result1 = await workflow.run("你好")
    # print("简单对话:", result1)

    # 任务请求
    result2 = await workflow.run("广济药业虚增营收的财务造假违规具体是违规在哪")
    print("任务请求:", result2)


if __name__ == "__main__":
    import asyncio

    print("=== 简化的智能工作流框架 ===")
    print("核心改进：")
    print("1. 去除冗余的工作级规划层")
    print("2. 使用枚举类型替代魔术字符串")
    print("3. 分离不可变状态和可变状态")
    print("4. 增加任务失败处理、重试机制、并发控制")
    print("5. 改进降级逻辑和错误处理")
    print()

    asyncio.run(example_usage())
