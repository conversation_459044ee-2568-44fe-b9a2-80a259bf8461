"""
测试重构后的智能体工作流框架
验证工具调用、消息驱动和动态路由功能
"""

import asyncio
import json
from agentic_workflow_framework import create_agentic_workflow


async def test_simple_chat():
    """测试简单对话功能"""
    print("=== 测试简单对话 ===")
    workflow = create_agentic_workflow()
    
    test_cases = [
        "你好",
        "谢谢",
        "测试",
        "今天天气怎么样？"
    ]
    
    for case in test_cases:
        print(f"\n用户: {case}")
        try:
            result = await workflow.run(case)
            print(f"助手: {result}")
        except Exception as e:
            print(f"错误: {e}")


async def test_query_tasks():
    """测试查询任务功能"""
    print("\n=== 测试查询任务 ===")
    workflow = create_agentic_workflow()
    
    test_cases = [
        "查询违规案例",
        "搜索信息披露违规的案例",
        "查找财务造假相关的处罚案例",
        "分析近期的违规趋势"
    ]
    
    for case in test_cases:
        print(f"\n用户: {case}")
        try:
            result = await workflow.run(case)
            print(f"助手: {result}")
        except Exception as e:
            print(f"错误: {e}")


async def test_chart_generation():
    """测试图表生成功能"""
    print("\n=== 测试图表生成 ===")
    workflow = create_agentic_workflow()
    
    test_cases = [
        "查询违规案例并生成图表",
        "分析信息披露违规案例，用图表展示",
        "统计各类违规案例的数量，生成可视化图表"
    ]
    
    for case in test_cases:
        print(f"\n用户: {case}")
        try:
            result = await workflow.run(case)
            print(f"助手: {result}")
        except Exception as e:
            print(f"错误: {e}")


async def test_workflow_state():
    """测试工作流状态管理"""
    print("\n=== 测试工作流状态 ===")
    workflow = create_agentic_workflow()
    
    # 编译图并获取初始状态
    compiled_graph = workflow.compile()
    initial_state = workflow.get_initial_state("查询违规案例")
    
    print("初始状态:")
    print(json.dumps(initial_state, ensure_ascii=False, indent=2))
    
    try:
        # 执行工作流
        result = await compiled_graph.ainvoke(initial_state)
        print("\n最终状态:")
        print(f"消息数量: {len(result.get('messages', []))}")
        
        # 打印所有消息
        for i, msg in enumerate(result.get('messages', [])):
            print(f"消息 {i+1}: {type(msg).__name__} - {getattr(msg, 'content', 'N/A')[:100]}...")
            
    except Exception as e:
        print(f"错误: {e}")


async def test_tool_calls():
    """测试工具调用功能"""
    print("\n=== 测试工具调用 ===")
    
    # 直接测试工具
    from agentic_workflow_framework import vector_database_search, generate_chart
    
    print("测试向量搜索工具:")
    try:
        search_result = await vector_database_search.ainvoke({"query": "信息披露违规"})
        print(f"搜索结果类型: {type(search_result)}")
        print(f"搜索结果: {str(search_result)[:200]}...")
    except Exception as e:
        print(f"搜索工具错误: {e}")
    
    print("\n测试图表生成工具:")
    try:
        chart_result = await generate_chart.ainvoke({"search_results": "测试数据"})
        print(f"图表结果类型: {type(chart_result)}")
        print(f"图表结果: {str(chart_result)[:200]}...")
    except Exception as e:
        print(f"图表工具错误: {e}")


async def main():
    """主测试函数"""
    print("开始测试重构后的智能体工作流框架")
    print("=" * 50)
    
    # 运行各项测试
    await test_simple_chat()
    await test_query_tasks()
    await test_chart_generation()
    await test_workflow_state()
    await test_tool_calls()
    
    print("\n" + "=" * 50)
    print("测试完成")


if __name__ == "__main__":
    asyncio.run(main())
