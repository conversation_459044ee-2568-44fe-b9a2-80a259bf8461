"""
增强版工作流框架 - 支持 Function Call 和 Tools 注解
基于原有框架，增加了工具注册装饰器和 function call 支持
"""
import json
import inspect
from typing import Annotated, Optional, Dict, Any, List, Callable, get_type_hints
from functools import wraps
from dataclasses import dataclass
from langchain_core.messages import AnyMessage, AIMessage
from langchain_core.tools import BaseTool
from pydantic import BaseModel, Field

from .generic_workflow_framework import (
    GenericWorkflowFramework, 
    GenericWorkflowState,
    TaskState,
    Task,
    ToolRegistry
)
from utils.load_env import logger


@dataclass
class ToolMetadata:
    """工具元数据"""
    name: str
    description: str
    parameters: Dict[str, Any]
    function: Callable
    tool_type: str = "function_call"


class EnhancedToolRegistry(ToolRegistry):
    """增强版工具注册表，支持装饰器注册和 function call"""
    
    def __init__(self):
        super().__init__()
        self._tool_metadata: Dict[str, ToolMetadata] = {}
        self._function_tools: Dict[str, Callable] = {}
    
    def tool(self, 
             name: str = None, 
             description: str = None,
             tool_type: str = "function_call"):
        """工具注册装饰器
        
        Args:
            name: 工具名称，默认使用函数名
            description: 工具描述，默认使用函数文档字符串
            tool_type: 工具类型，用于分类管理
        """
        def decorator(func: Callable) -> Callable:
            tool_name = name or func.__name__
            tool_desc = description or func.__doc__ or f"执行 {tool_name} 操作"
            
            # 解析函数参数
            sig = inspect.signature(func)
            type_hints = get_type_hints(func)
            
            parameters = {}
            for param_name, param in sig.parameters.items():
                if param_name in ['self', 'cls']:
                    continue
                    
                param_info = {
                    "type": self._get_param_type(type_hints.get(param_name, str)),
                    "required": param.default == inspect.Parameter.empty,
                }
                
                # 从注解中提取描述
                if hasattr(param.annotation, '__metadata__'):
                    for meta in param.annotation.__metadata__:
                        if isinstance(meta, str):
                            param_info["description"] = meta
                            break
                
                parameters[param_name] = param_info
            
            # 创建工具元数据
            metadata = ToolMetadata(
                name=tool_name,
                description=tool_desc,
                parameters=parameters,
                function=func,
                tool_type=tool_type
            )
            
            # 注册工具
            self._tool_metadata[tool_name] = metadata
            self._function_tools[tool_name] = func
            
            # 创建执行器包装器
            executor = FunctionCallExecutor(func, metadata)
            self.register(tool_name, executor)
            
            logger.info(f"注册工具: {tool_name} ({tool_type})")
            
            @wraps(func)
            def wrapper(*args, **kwargs):
                return func(*args, **kwargs)
            
            return wrapper
        
        return decorator
    
    def _get_param_type(self, type_hint) -> str:
        """获取参数类型字符串"""
        if type_hint == str:
            return "string"
        elif type_hint == int:
            return "integer"
        elif type_hint == float:
            return "number"
        elif type_hint == bool:
            return "boolean"
        elif type_hint == list:
            return "array"
        elif type_hint == dict:
            return "object"
        else:
            return "string"
    
    def get_tool_metadata(self, tool_name: str) -> Optional[ToolMetadata]:
        """获取工具元数据"""
        return self._tool_metadata.get(tool_name)
    
    def list_tools_metadata(self) -> List[ToolMetadata]:
        """列出所有工具元数据"""
        return list(self._tool_metadata.values())
    
    def get_function_call_schema(self) -> List[Dict[str, Any]]:
        """获取 function call 格式的工具定义"""
        schemas = []
        for metadata in self._tool_metadata.values():
            schema = {
                "type": "function",
                "function": {
                    "name": metadata.name,
                    "description": metadata.description,
                    "parameters": {
                        "type": "object",
                        "properties": metadata.parameters,
                        "required": [
                            name for name, info in metadata.parameters.items() 
                            if info.get("required", False)
                        ]
                    }
                }
            }
            schemas.append(schema)
        return schemas


class FunctionCallExecutor:
    """Function Call 执行器"""
    
    def __init__(self, function: Callable, metadata: ToolMetadata):
        self.function = function
        self.metadata = metadata
    
    async def execute(self, task: Dict[str, Any], context: Dict[str, Any] = None) -> str:
        """执行 function call"""
        try:
            # 从任务中提取参数
            function_args = task.get("function_args", {})
            
            # 如果函数是异步的
            if inspect.iscoroutinefunction(self.function):
                result = await self.function(**function_args)
            else:
                result = self.function(**function_args)
            
            # 格式化返回结果
            if isinstance(result, (dict, list)):
                return json.dumps(result, ensure_ascii=False, indent=2)
            else:
                return str(result)
                
        except Exception as e:
            logger.error(f"执行工具 {self.metadata.name} 失败: {e}")
            return f"工具执行失败: {str(e)}"


class EnhancedPlanner:
    """增强版规划器，支持 function call 工具选择"""
    
    def __init__(self, llm, tool_registry: EnhancedToolRegistry):
        self.llm = llm
        self.tool_registry = tool_registry
    
    async def plan_with_tools(self, message: AnyMessage) -> Dict[str, Any]:
        """使用 function call 进行规划"""
        try:
            # 获取可用工具
            tools_schema = self.tool_registry.get_function_call_schema()
            
            # 构建包含工具信息的提示
            tools_info = "\n".join([
                f"- {tool['function']['name']}: {tool['function']['description']}"
                for tool in tools_schema
            ])
            
            # 使用 LLM 进行规划（这里可以集成 function call）
            planning_prompt = f"""
            用户请求: {message.content}
            
            可用工具:
            {tools_info}
            
            请分析用户需求，选择合适的工具并制定执行计划。
            """
            
            # 这里可以使用支持 function call 的 LLM
            # 暂时使用简化版本
            response = await self.llm.ainvoke(planning_prompt)
            
            # 解析响应并生成任务
            # 实际实现中可以解析 function call 结果
            
            return {
                "user_goal": str(message.content),
                "approach": "使用 function call 工具执行",
                "initial_tasks": [{
                    "id": "task_1",
                    "description": str(message.content),
                    "query": str(message.content),
                    "tool_type": "query_tool",  # 这里可以动态选择
                    "intent": "获取信息"
                }],
                "need_chart": False
            }
            
        except Exception as e:
            logger.error(f"增强规划失败: {e}")
            # 降级到原有规划逻辑
            return await self._fallback_plan(message)
    
    async def _fallback_plan(self, message: AnyMessage) -> Dict[str, Any]:
        """降级规划方案"""
        return {
            "user_goal": str(message.content),
            "approach": "直接查询获取信息",
            "initial_tasks": [{
                "id": "task_1",
                "description": str(message.content),
                "query": str(message.content),
                "tool_type": "query_tool",
                "intent": "获取信息"
            }],
            "need_chart": False
        }


class EnhancedWorkflowFramework(GenericWorkflowFramework):
    """增强版工作流框架"""
    
    def __init__(self, llm):
        # 使用增强版工具注册表
        self.enhanced_tool_registry = EnhancedToolRegistry()
        
        # 初始化父类
        super().__init__(llm)
        
        # 替换工具注册表
        self.tool_registry = self.enhanced_tool_registry
        
        # 使用增强版规划器
        self.enhanced_planner = EnhancedPlanner(llm, self.enhanced_tool_registry)
        
        # 注册默认工具
        self._register_default_tools()
    
    def _register_default_tools(self):
        """注册默认工具"""
        # 这里可以注册一些默认工具
        pass
    
    def get_tool_registry(self) -> EnhancedToolRegistry:
        """获取增强版工具注册表"""
        return self.enhanced_tool_registry
    
    async def _enhanced_planning_node(self, state: GenericWorkflowState) -> Dict[str, Any]:
        """增强版规划节点"""
        messages = state.get("messages", [])
        
        if not messages:
            return {"current_tasks": [], "task_results": []}
        
        last_message = messages[-1]
        result = await self.enhanced_planner.plan_with_tools(last_message)
        
        return {
            "user_goal": result.get("user_goal"),
            "approach": result.get("approach"),
            "current_tasks": result.get("initial_tasks", []),
            "task_results": [],
            "iteration_count": 0,
            "need_chart": result.get("need_chart"),
            "need_character_relationships": result.get("need_character_relationships")
        }


# 便捷函数
def create_enhanced_workflow(llm=None):
    """创建增强版工作流实例"""
    if llm is None:
        from sub_agents.rep.utils.llm_utils import get_a_llm
        llm = get_a_llm(llm_type='hs-deepseek-v3-0324', temperature=0)
    
    return EnhancedWorkflowFramework(llm)


# 使用示例和实际工具实现
def example_tool_registration():
    """工具注册示例"""

    # 创建工作流
    workflow = create_enhanced_workflow()
    registry = workflow.get_tool_registry()

    # 使用装饰器注册工具
    @registry.tool(
        name="search_violations",
        description="搜索违规案例数据",
        tool_type="vector_search"
    )
    async def search_violations(
        query: Annotated[str, "搜索查询内容"],
        limit: Annotated[int, "返回结果数量"] = 10
    ) -> Dict[str, Any]:
        """搜索违规案例"""
        from ..core.vector_search import execute_multi_query_rerank_search
        result = await execute_multi_query_rerank_search(query)
        return result

    @registry.tool(
        name="analyze_data",
        description="分析数据并生成报告"
    )
    async def analyze_data(
        data: Annotated[str, "要分析的数据"],
        analysis_type: Annotated[str, "分析类型"] = "summary"
    ) -> str:
        """分析数据"""
        return f"对数据进行 {analysis_type} 分析的结果"

    @registry.tool(
        name="calculate_metrics",
        description="计算统计指标"
    )
    async def calculate_metrics(
        data: Annotated[List[Dict], "数据列表"],
        metric_type: Annotated[str, "指标类型"] = "count"
    ) -> Dict[str, Any]:
        """计算统计指标"""
        if metric_type == "count":
            return {"total_count": len(data)}
        elif metric_type == "summary":
            return {"summary": "数据统计摘要"}
        return {"result": "计算完成"}

    return workflow, registry


# 工具管理器
class ToolManager:
    """工具管理器，提供工具的动态加载和管理"""

    def __init__(self, registry: EnhancedToolRegistry):
        self.registry = registry

    def load_tools_from_module(self, module_path: str):
        """从模块加载工具"""
        import importlib
        try:
            module = importlib.import_module(module_path)
            # 扫描模块中的工具函数
            for name in dir(module):
                obj = getattr(module, name)
                if hasattr(obj, '_tool_metadata'):
                    # 如果是已注册的工具函数
                    self.registry.register(name, obj)
                    logger.info(f"从模块 {module_path} 加载工具: {name}")
        except Exception as e:
            logger.error(f"加载模块 {module_path} 失败: {e}")

    def get_tool_usage_stats(self) -> Dict[str, Any]:
        """获取工具使用统计"""
        tools = self.registry.list_tools_metadata()
        return {
            "total_tools": len(tools),
            "tool_types": list(set(tool.tool_type for tool in tools)),
            "tools_by_type": {
                tool_type: [tool.name for tool in tools if tool.tool_type == tool_type]
                for tool_type in set(tool.tool_type for tool in tools)
            }
        }


# 迭代检查增强
class EnhancedIterationDecider:
    """增强版迭代决策器，支持工具推荐"""

    def __init__(self, llm, tool_registry: EnhancedToolRegistry):
        self.llm = llm
        self.tool_registry = tool_registry

    async def decide_with_tools(self,
                               user_goal: str,
                               approach: str,
                               current_results: List[Dict],
                               available_tools: List[str] = None) -> Dict[str, Any]:
        """基于可用工具进行迭代决策"""

        # 获取可用工具信息
        if available_tools is None:
            available_tools = [tool.name for tool in self.tool_registry.list_tools_metadata()]

        tools_info = []
        for tool_name in available_tools:
            metadata = self.tool_registry.get_tool_metadata(tool_name)
            if metadata:
                tools_info.append(f"- {tool_name}: {metadata.description}")

        # 分析当前结果
        results_summary = "\n".join([
            f"{r.get('description', '')}: {r.get('result', '')}"
            for r in current_results
        ])

        # 构建决策提示
        decision_prompt = f"""
        用户目标: {user_goal}
        解决思路: {approach}
        当前结果: {results_summary}

        可用工具:
        {chr(10).join(tools_info)}

        请分析是否需要使用其他工具来完善结果。
        """

        try:
            # 使用 LLM 进行决策
            response = await self.llm.ainvoke(decision_prompt)

            # 这里可以解析 LLM 的响应来决定下一步
            # 简化版本：基于关键词判断
            content = response.content.lower()

            if any(keyword in content for keyword in ["需要", "补充", "更多", "继续"]):
                return {
                    "need_more_tasks": True,
                    "new_tasks": [{
                        "id": "additional_task",
                        "description": "补充分析",
                        "query": user_goal,
                        "tool_type": "analyze_data",
                        "intent": "深度分析"
                    }],
                    "reasoning": "需要进一步分析",
                    "recommended_tools": available_tools[:3]  # 推荐前3个工具
                }
            else:
                return {
                    "need_more_tasks": False,
                    "new_tasks": [],
                    "reasoning": "当前结果已足够",
                    "recommended_tools": []
                }

        except Exception as e:
            logger.error(f"增强迭代决策失败: {e}")
            return {
                "need_more_tasks": False,
                "new_tasks": [],
                "reasoning": "决策失败，停止迭代",
                "recommended_tools": []
            }
