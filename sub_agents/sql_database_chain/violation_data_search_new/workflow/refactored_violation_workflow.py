"""
重构后的违规案例查询工作流
从工作流模式转换为真正的智能体模式
"""
import json
from typing import Annotated, Optional, Dict, Any, List, Literal
from datetime import datetime
import pytz

from langchain_core.messages import AnyMessage, AIMessage, HumanMessage, ToolMessage
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.tools import tool
from langgraph.config import get_stream_writer
from langgraph.graph import StateGraph, START, END, add_messages
from langgraph.prebuilt import ToolNode
from typing_extensions import TypedDict

from sub_agents.rep.utils.llm_utils import get_a_llm
from sub_agents.sql_database_chain.violation_data_search_new.core.vector_search import execute_multi_query_rerank_search
from utils.load_env import logger


# 简化的状态定义
class ViolationAgentState(TypedDict):
    """违规案例智能体状态"""
    messages: Annotated[list[AnyMessage], add_messages]
    context: Optional[Dict[str, Any]]


# 工具定义 - 将原有的执行器转换为工具
@tool
async def search_violation_cases(query: str) -> dict:
    """
    搜索违规案例数据库。
    当需要查询具体的违规案例、公司信息或相关法规时使用。
    
    Args:
        query: 搜索查询语句，可以是公司名称、违规类型、时间范围等
        
    Returns:
        包含搜索结果的字典，包含案例详情、统计信息等
    """
    try:
        logger.info(f"[ViolationSearchTool] 执行违规案例搜索: {query}")
        
        # 发送流程信息
        try:
            writer = get_stream_writer()
            writer(json.dumps({"AI_AGENT_FLOW": f"🔍 正在搜索违规案例: {query}"}, ensure_ascii=False))
        except Exception:
            pass
            
        # 调用原有的向量搜索逻辑
        result_dict = await execute_multi_query_rerank_search(query)
        
        # 推送向量检索数据
        try:
            writer = get_stream_writer()
            writer(json.dumps({'AI_AGENT_VECTOR': result_dict}, ensure_ascii=False))
        except Exception:
            pass
            
        return result_dict
        
    except Exception as e:
        logger.error(f"[ViolationSearchTool] 搜索失败: {e}")
        return {"error": f"搜索失败: {str(e)}", "search_summary": "", "formatted_results": []}


@tool
async def generate_violation_chart(analysis_data: str) -> dict:
    """
    根据违规案例分析结果生成可视化图表。
    当分析结果适合用图表展示时使用，如趋势分析、统计对比等。
    
    Args:
        analysis_data: 分析数据摘要，包含需要可视化的信息
        
    Returns:
        包含ECharts图表配置的字典
    """
    try:
        logger.info(f"[ViolationChartTool] 生成违规案例图表")
        
        # 发送流程信息
        try:
            writer = get_stream_writer()
            writer(json.dumps({"AI_AGENT_FLOW": "📈 正在生成违规案例可视化图表..."}, ensure_ascii=False))
        except Exception:
            pass
        
        # 这里可以集成您原有的图表生成逻辑
        # 简化示例 - 实际应该调用 ChartGenerator
        chart_config = {
            "id": "violation_chart_001",
            "name": "违规案例分析图表",
            "type": "bar",
            "option": {
                "title": {"text": "违规案例统计分析"},
                "tooltip": {},
                "legend": {},
                "dataset": {
                    "source": [
                        ["类型", "案例数量"],
                        ["内幕交易", 45],
                        ["信息披露违规", 32],
                        ["操纵市场", 28],
                        ["其他违规", 15]
                    ]
                },
                "xAxis": {"type": "category"},
                "yAxis": {},
                "series": [{"type": "bar"}]
            }
        }
        
        # 推送图表数据
        try:
            writer = get_stream_writer()
            writer(json.dumps({"AI_AGENT_CHART": chart_config}, ensure_ascii=False))
        except Exception:
            pass
            
        return chart_config
        
    except Exception as e:
        logger.error(f"[ViolationChartTool] 图表生成失败: {e}")
        return {"error": f"图表生成失败: {str(e)}"}


# 工具列表
violation_tools = [search_violation_cases, generate_violation_chart]
violation_tool_node = ToolNode(violation_tools)


class ViolationSupervisorAgent:
    """违规案例监督智能体 - 负责意图识别"""
    
    def __init__(self, llm):
        self.llm = llm
        self.prompt = ChatPromptTemplate.from_messages([
            ("system", """你是违规案例查询系统的监督者，负责识别用户意图。

判断用户输入类型：

1. **simple_chat（简单对话）**：
   - 问候语：你好、hi、hello等
   - 感谢语：谢谢、感谢等  
   - 闲聊：天气、心情等无关话题
   - 测试输入：测试、test等

2. **violation_query（违规案例查询）**：
   - 任何关于违规案例的查询
   - 公司违规信息查询
   - 违规类型分析
   - 监管处罚信息等

处理方式：
- 简单对话：直接生成友好回复
- 违规查询：回复 "DELEGATE_TO_ANALYST" 委托给分析师

当前时间：{time}
用户输入：{user_input}"""),
        ]).partial(time=datetime.now(pytz.timezone('Asia/Shanghai')).strftime("%Y年%m月%d日 %H点%M分"))
    
    async def process(self, messages: List[AnyMessage]) -> AIMessage:
        """处理用户输入"""
        try:
            user_input = messages[-1].content if messages else ""
            
            # 发送流程信息
            try:
                writer = get_stream_writer()
                writer(json.dumps({"AI_AGENT_FLOW": "🤔 监督者正在理解您的需求..."}, ensure_ascii=False))
            except Exception:
                pass
            
            chain = self.prompt | self.llm
            response = await chain.ainvoke({"user_input": user_input})
            
            return AIMessage(content=response.content)
            
        except Exception as e:
            logger.error(f"[ViolationSupervisor] 处理失败: {e}")
            return AIMessage(content="DELEGATE_TO_ANALYST")


class ViolationAnalystAgent:
    """违规案例分析师智能体 - 核心分析逻辑"""
    
    def __init__(self, llm):
        self.llm_with_tools = llm.bind_tools(violation_tools)
        self.prompt = ChatPromptTemplate.from_messages([
            ("system", """你是专业的违规案例分析师，专门处理证券市场违规案例查询和分析。

你的专业能力：
1. **违规案例搜索**：使用 search_violation_cases 工具搜索相关案例
2. **数据分析**：深度分析违规案例的模式、趋势、影响
3. **可视化展示**：使用 generate_violation_chart 工具生成图表
4. **专业解读**：提供专业的违规案例解读和监管解析

工作流程：
1. 理解用户查询需求
2. 使用搜索工具获取相关违规案例
3. 分析搜索结果，判断是否需要更多信息
4. 如果数据适合可视化，生成相应图表
5. 提供专业的分析报告

重要原则：
- 基于实际搜索结果进行分析，不编造信息
- 可以多次搜索以获得更全面的信息
- 主动识别可视化需求并生成图表
- 提供专业但易懂的分析解读

当前时间：{time}"""),
            ("placeholder", "{messages}"),
        ]).partial(time=datetime.now(pytz.timezone('Asia/Shanghai')).strftime("%Y年%m月%d日 %H点%M分"))
    
    async def process(self, messages: List[AnyMessage]) -> AIMessage:
        """处理分析请求"""
        try:
            # 发送流程信息
            try:
                writer = get_stream_writer()
                writer(json.dumps({"AI_AGENT_FLOW": "🧠 违规案例分析师正在制定分析策略..."}, ensure_ascii=False))
            except Exception:
                pass
            
            chain = self.prompt | self.llm_with_tools
            response = await chain.ainvoke({"messages": messages})
            
            return response
            
        except Exception as e:
            logger.error(f"[ViolationAnalyst] 处理失败: {e}")
            return AIMessage(content=f"分析过程中出现错误：{str(e)}")


class RefactoredViolationWorkflow:
    """重构后的违规案例工作流 - 智能体模式"""
    
    def __init__(self, llm):
        self.llm = llm
        
        # 初始化智能体
        self.supervisor = ViolationSupervisorAgent(llm)
        self.analyst = ViolationAnalystAgent(llm)
        
        # 构建图
        self.graph = self._build_graph()
    
    def _build_graph(self) -> StateGraph:
        """构建智能体交互图"""
        graph = StateGraph(ViolationAgentState)
        
        # 添加节点
        graph.add_node("supervisor", self._supervisor_node)
        graph.add_node("analyst", self._analyst_node)
        graph.add_node("tools", violation_tool_node)
        
        # 设置流程
        graph.add_edge(START, "supervisor")
        
        # 监督者路由
        graph.add_conditional_edges(
            "supervisor",
            self._supervisor_router,
            {
                "analyst": "analyst",
                "end": END
            }
        )
        
        # 分析师路由
        graph.add_conditional_edges(
            "analyst",
            self._should_continue,
            {
                "tools": "tools",
                "end": END
            }
        )
        
        # 工具执行后返回分析师
        graph.add_edge("tools", "analyst")
        
        return graph
    
    async def _supervisor_node(self, state: ViolationAgentState) -> Dict[str, Any]:
        """监督者节点"""
        messages = state.get("messages", [])
        response = await self.supervisor.process(messages)
        return {"messages": [response]}
    
    async def _analyst_node(self, state: ViolationAgentState) -> Dict[str, Any]:
        """分析师节点"""
        messages = state.get("messages", [])
        response = await self.analyst.process(messages)
        return {"messages": [response]}
    
    def _supervisor_router(self, state: ViolationAgentState) -> Literal["analyst", "end"]:
        """监督者路由决策"""
        last_message = state["messages"][-1]
        if "DELEGATE_TO_ANALYST" in last_message.content:
            return "analyst"
        return "end"
    
    def _should_continue(self, state: ViolationAgentState) -> Literal["tools", "end"]:
        """判断是否需要继续调用工具"""
        last_message = state["messages"][-1]
        
        # 如果最后一条消息包含工具调用，则路由到工具节点
        if hasattr(last_message, 'tool_calls') and last_message.tool_calls:
            return "tools"
        
        return "end"
    
    def compile(self, **kwargs):
        """编译图"""
        return self.graph.compile(**kwargs)
    
    def get_initial_state(self, message: str, **kwargs) -> Dict[str, Any]:
        """获取初始状态"""
        return {
            "messages": [HumanMessage(content=message)],
            "context": kwargs.get("context", {})
        }


# 便捷函数
def create_refactored_violation_workflow(llm=None):
    """创建重构后的违规案例工作流"""
    if llm is None:
        llm = get_a_llm(llm_type='hs-deepseek-v3-0324', temperature=0)
    
    return RefactoredViolationWorkflow(llm)


# 使用示例
async def test_refactored_workflow():
    """测试重构后的工作流"""
    # 创建工作流
    workflow = create_refactored_violation_workflow()
    compiled_graph = workflow.compile()
    
    # 测试查询
    test_queries = [
        "你好",
        "查询腾讯公司的违规案例",
        "分析2023年内幕交易违规案例的趋势，需要图表展示"
    ]
    
    for query in test_queries:
        print(f"\n查询: {query}")
        state = workflow.get_initial_state(query)
        result = await compiled_graph.ainvoke(state)
        print(f"回复: {result['messages'][-1].content}")


if __name__ == "__main__":
    import asyncio
    print("=== 重构后的违规案例工作流 ===")
    asyncio.run(test_refactored_workflow())
