"""
测试 original_workflow.py 修改后的功能
验证新的返回格式是否正确工作
"""
import asyncio
import json
from sub_agents.sql_database_chain.violation_data_search_new.core.original_workflow import execute_violation_query


async def test_new_return_format():
    """测试新的返回格式"""
    print("=== 测试 original_workflow 新的返回格式 ===")
    
    # 测试查询
    query = "查询2023年财务造假案例"
    
    try:
        result = await execute_violation_query(query)
        
        print(f"查询: {query}")
        print(f"返回类型: {type(result)}")
        print(f"返回结果:")
        print(json.dumps(result, ensure_ascii=False, indent=2))
        
        # 验证返回格式
        assert isinstance(result, dict), "返回结果应该是字典"
        assert "AI_AGENT_RESPONSE" in result, "应该包含 AI_AGENT_RESPONSE 字段"
        assert "last_tool_message" in result, "应该包含 last_tool_message 字段"
        
        print("\n✅ 测试通过：返回格式正确")
        
        # 打印具体内容
        ai_response = result.get("AI_AGENT_RESPONSE")
        tool_message = result.get("last_tool_message")
        
        print(f"\nAI_AGENT_RESPONSE: {ai_response}")
        print(f"last_tool_message: {tool_message}")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(test_new_return_format())
